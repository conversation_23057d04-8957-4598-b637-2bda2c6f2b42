<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="LaravelPint">
    <laravel_pint_settings>
      <LaravelPintConfiguration asDefaultInterpreter="true" />
    </laravel_pint_settings>
  </component>
  <component name="MessDetector">
    <phpmd_settings>
      <MessDetectorConfiguration asDefaultInterpreter="true" />
    </phpmd_settings>
  </component>
  <component name="PhpCSFixer">
    <phpcsfixer_settings>
      <PhpCSFixerConfiguration asDefaultInterpreter="true" />
    </phpcsfixer_settings>
  </component>
  <component name="PhpCodeSniffer">
    <phpcs_settings>
      <PhpCSConfiguration asDefaultInterpreter="true" />
    </phpcs_settings>
  </component>
  <component name="PhpProjectSharedConfiguration" php_language_level="7.2">
    <option name="suggestChangeDefaultLanguageLevel" value="false" />
  </component>
</project>
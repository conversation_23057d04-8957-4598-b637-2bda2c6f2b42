<?php
namespace Back\Controller;
use Think\Controller;
use Vendor\Unlimit;
class ArtController extends CommController {
	public function _initialize(){
			parent::_initialize();
	}

	function getChild(){
		if(I('get.id')){
			$lists = M('Cat')->where(['pid'=>I('get.id')])->order('order_id desc, id asc')->select();
			die(json_encode($lists));
		}
		
	}


	public function index(){
		$where = '`cat_id` = '.I('get.cid');


		if(I('get.key')){
			$where.=' and `title` like "%'.I('get.key').'%"';
		}
		// print_r($where);
		$count =  M('Art')->where($where)->count();
		$page = new \Think\Page($count,10);
		$page->setConfig('theme', "%UP_PAGE% %LINK_PAGE% %DOWN_PAGE% %END% <label class='f_14 clr_6'>共%TOTAL_ROW%条</label>");
		$this->page = $page->show();
		$lists = M('Art')->where($where)->order('order_id asc,id desc')->limit($page->firstRow.','.$page->listRows)->select();
		// foreach($lists as $k=>$v){

		// }
		$this->lists = $lists;
		// print_r(M('Art')->_sql());
		$this->display();
		
	}
	function runCheck(){
		if(I('get.status')){
			M('Art')->where(array('id'=>I('get.id')))->save(array('status'=>I('get.status')));
			die(json_encode(array('code'=>1)));
		}
	}
	public function add(){
		$this->display('add_tit_cont');
		
		
	}
	public function runAdd(){
		$a = M('Art');
		if($a->create()){
				$a->add_time = I('post.pub_date') == '' ? time() :  strtotime(I('post.pub_date'));
				$a->pub_date = strtotime(I('post.pub_date'));
				$a->content = $_POST['content'];
				$a->order_id = 100;

				if($a->add()){
					//$this->show($a->_sql());
					die(json_encode(array('code'=>1)));
				}
		}
	}
	public function edit(){
		$this->res = $res = M('Art')->find(I('get.id'));

		$this->display('edit_tit_cont');
	}
	public function runEdit(){
		$a = M('Art');
		if($a->create()){
			$a->add_time =I('post.pub_date') == '' ? time() :  strtotime(I('post.pub_date'));
			$a->pub_date = strtotime(I('post.pub_date'));
			$a->content = $_POST['content'];

			$a->save();
			// $this->redirect('index', array('cid'=>I('post.cid'),'type'=>I('post.type')));
			die(json_encode(array('code'=>1)));
		}
	}
	function delAll(){
		if(I('post.id')){
			foreach(I('post.id') as $k=>$v){
				$res = M('Art')->find($v);
				//删除图片
				if($res['thumbs']!=''){
					@unlink(realpath('.').'/Attached/'.$res['thumbs']);
				}
				M('Art')->delete($res['id']);
			}
			die(json_encode(array('code'=>1)));
		}
	}
	public function del(){
		$res = M('Art')->find(I('get.id'));
		//删除图片
		if($res['thumbs']!=''){
			@unlink(realpath('.').'/Attached/'.$res['thumbs']);
		}
		if(M('Art')->delete(I('get.id'))){
			die(json_encode(array('code'=>1)));
		}
	}
	public function runOrder(){
		M('Art')->where(array('id'=>I('get.id')))->save(array('order_id'=>I('get.order_id')));
		die(json_encode(array('code'=>1, 'msg'=>'操作成功')));
	}
	public function runRecom(){
		$res = M('Art')->find(I('get.id'));
		if($res['is_recom']==1){
			M('Art')->where(array('id'=>$res['id']))->save(array('is_recom'=>0));
		}else{
			M('Art')->where(array('id'=>$res['id']))->save(array('is_recom'=>1));
		}
		die(json_encode(array('code'=>1, 'msg'=>'操作成功')));
	}

}
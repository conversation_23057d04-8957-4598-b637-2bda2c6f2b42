<?php
namespace Back\Controller;
use Think\Controller;
class GongchangController extends CommController {
	public function _initialize(){
		parent::_initialize();
		$this->xj_level1 = M('Cat')->where(['pid'=>329])->order('order_id desc, id asc')->select();
		$this->jiaohuodi= C('xls_jiaohuodi');
		// print_r($this->xj_level1 );
	}
	public function add(){
		$this->display();
	}
	public function runAdd(){
		$t = M('Gongchang');
		$t->create();
		$t->order_id = 100;
		$t->add_time = time();
		$t->add();
		die(json_encode(['code'=>1, 'msg'=>'操作成功']));
	}
	public function index(){

		$where = '`id` >0 ';
		if(I('get.key')){
			$where .= ' and  `title` like "%'.I('get.key').'%"';
		}
		$count =  M('Gongchang')->where($where)->count();
		$page = new \Think\Page($count,10);
		$page->setConfig('theme', "%UP_PAGE% %LINK_PAGE% %DOWN_PAGE% %END% <label class='f_14 clr_6'>共%TOTAL_ROW%条</label>");
		$this->page = $page->show();
		$lists = M('Gongchang')->where($where )->order('add_time desc')->limit($page->firstRow.','.$page->listRows)->select();
		foreach($lists as $k=>$v){
			$lists[$k]['diqu'] = M('Cat')->where(['id'=>$v['level1']])->getField('name')
				.' '.M('Cat')->where(['id'=>$v['level2']])->getField('name')
				.' '.M('Cat')->where(['id'=>$v['level3']])->getField('name');
		}
		// print_r($lists);
		$this->lists = $lists;
		$this->display();
	}
	public function toExcel(){
		$where = '`id` >0 ';
		$lists = M('Gongchang')->where($where )->order('id desc')->limit(1000)->select();
		header("Content-Type: application/vnd.ms-excel;charset=UTF-8");
		header("Content-Disposition: attachment; filename=".iconv('utf-8', 'gb2312', '用户管理_'). date('Y-m-d').".xls");
		header("Pragma: no-cache");
		header("Expires: 0");
		echo("<meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"/>");
		echo('<table border=1>');
		echo('<tr><th>姓名</th><th>加入时间</th></tr>');
		foreach ($lists as $k=>$v){
			echo('<td  width="300" align="center">'.$v['nickname'].'</td>');
			echo('<td width="200" align="center">&nbsp; '.date('Y-m-d H:i', $v['add_time']).'</td></tr>');
		}
		echo('</table>');
	}
	function delAll(){
		if(I('post.id')){
			foreach(I('post.id') as $k=>$v){
				$res = M('Gongchang')->find($v);
				//删除图片
				if($res['thumbs']!=''){
					@unlink(realpath('.').'/Attached/'.$res['thumbs']);
				}
				M('Gongchang')->delete($res['id']);
			}
			die(json_encode(array('code'=>1)));
		}
	}
	public function del(){
		$res = M('Gongchang')->find(I('get.id'));
		if($res['thumbs']!=''){
			@unlink(realpath('.').'/Attached/'.$res['thumbs']);
		}
		M('Gongchang')->delete(I('get.id'));
		die(json_encode(array('code'=>1)));
	}
	public function isRecom(){
		$res = M('Gongchang')->find(I('get.id'));
		if($res['is_recom'] == 1){
			M('Gongchang')->where(array('id'=>$res['id']))->save(array('is_recom'=>0));
		}else{
			M('Gongchang')->where(array('id'=>$res['id']))->save(array('is_recom'=>1));
		}
		redirect($_SERVER['HTTP_REFERER']);
	}
	public function edit(){
		$res = M('Gongchang')->find(I('get.id'));

		$this->level2 = M('Cat')->where(['pid'=>$res['level1']])->order('order_id desc, id asc')->select();
		$this->level3 = M('Cat')->where(['pid'=>$res['level2']])->order('order_id desc, id asc')->select();

		$this->res = $res;
		$this->display();
	}
	public function runEdit(){
		$t = M('Gongchang');
		$t->create();
		// $t->add_time = time();
		if(I('post.password')!=''){
			$t->password = md5(I('post.password'));
		}else{
			unset($t->password);
		}
		$t->save();
		die(json_encode(array('code'=>1)));
	}
    public function runOrder(){
    		M('Gongchang')->where(array('id'=>I('get.id')))->save(array('order_id'=>I('get.order_id')));
    		die(json_encode(array('code'=>1, 'msg'=>'操作成功')));
    }
}
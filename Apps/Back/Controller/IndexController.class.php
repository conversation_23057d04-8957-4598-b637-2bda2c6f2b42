<?php
namespace Back\Controller;
use Think\Controller;
class IndexController extends Controller {
	public function index(){
		$this->rnd_img = mt_rand(1,5);
		$this->display();
	}
    public function runLogin(){
  		// $verify = new \Think\Verify();
  		// if(!$verify->check(I('post.captcha'), $id)){
  		// 	die(json_encode(array('code'=>0, 'msg'=>'验证码错误')));
  		// }
		if($res = M('Admin')->where(array('name'=>I('post.username')))->find()){
			if(md5(trim(I('post.password'))) == trim($res['pwd'])){
				session('login_type','');
				session('uid_admin',$res['id']);
				session('login_time_admin', time());
				//记录登录流水
				// M('Denglu')->add(['add_time'=>time(), 'uid'=>$res['id'], 'uid_text'=>I('post.username')]);
				if($res['type'] == 0){
					//管理员进配置项
					die(json_encode(array('code'=>1, 'msg'=>'', 'url'=>U('Main/index'))));
				}else{
					//修改密码
					die(json_encode(array('code'=>1, 'msg'=>'', 'url'=>U('Main/index'))));
				}
			}else{
				die(json_encode(array('code'=>0, 'msg'=>'用户名或密码错误')));
			}
		}else{
			die(json_encode(array('code'=>0, 'msg'=>'用户名或密码错误')));
		}
    }
	public function pwd(){
		$this->res = M('Admin')->where(array('id'=>session('uid_admin')))->find();
		$this->display();
	}
	public function runPwd(){
		$a =M('Admin');
		if($a->create()){
			if($a->pwd !=''){
				$a->pwd = md5(I('post.pwd'));
			}else{
				unset($a->pwd);
			}
			if($a->save()){
				$this->success('操作成功');
			}else{
				$this->error('操作失败','',1);
			}
		}
	}
	public function exits(){
		session('login_type',null);
		session('uid_admin', null);
		session('login_time_admin', null);
		$this->redirect('Index/index');
	}
}
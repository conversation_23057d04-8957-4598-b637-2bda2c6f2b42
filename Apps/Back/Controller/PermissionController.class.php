<?php

namespace Back\Controller;

use Think\Controller;



class PermissionController extends CommController {

	function _initialize(){

		parent::_initialize();

		//print_r($this->leftNav);

	}

	



	public function index(){

		$this->lists = M('Admin')->where(array('type'=>1))->select();

		$this->display();

	}



	function edit(){

		$res = M('Admin')->find(I('get.id'));

		$res['permission'] = unserialize($res['permission']);

		if($res['permission']['setting']!=''){

			$res['setting'] = explode(',', $res['permission']['setting']);

		}

		



		//print_r($res);

		$this->res = $res;

		$this->display();

	}



	function runEdit(){

		$a = M('Admin');

		$a->create();

		if(I('post.pwd')){

			$a->pwd = md5(I('post.pwd'));	

		}else{

			unset($a->pwd);

		}





		//组织权限字串

		$b = array();

		foreach($_POST as $k=>$v){

				//剔除用户、密码

			if($k != 'name' && $k != 'pwd'){

				$b[$k] = implode(',', $v);

			}

		}



		$a->permission = serialize($b);

		$a->save();

		die(json_encode(array('code'=>1)));

	}





	function del(){

		M('Admin')->delete(I('get.id'));

		die(json_encode(array('code'=>1)));

	}





	public function runAdd(){



		if(M('Admin')->where(array('type'=>1, 'name'=>I('post.name')))->find()){

			die(json_encode(array('code'=>0, 'msg'=>'管理员已经存在，请使用其他名称')));

		}





		$a = M('Admin');



		$a->create();

		$a->pwd = md5(I('post.pwd'));

		$a->type = 1;



		//组织权限字串

		$b = array();

		foreach($_POST as $k=>$v){

				//剔除用户名input、密码input

			if($k != 'name' && $k != 'pwd'){

				$b[$k] = implode(',', $v);

			}

		}



		$a->permission = serialize($b);

		$a->add();

		die(json_encode(array('code'=>1)));

	}



}
<?php
namespace Back\Controller;
use Think\Controller;

class PremiumDiscountFieldController extends CommController {
    
    function _initialize(){
        parent::_initialize();
        
        // 定义字段类型选项
        $this->field_types = array(
            'number' => '数值型',
            'string' => '字符串型',
            'select' => '选择型'
        );
    }

    /**
     * 条件字段列表
     */
    public function index(){
        $page = I('get.page', 1);
        $pageSize = 20;
        
        $model = M('PremiumDiscountConditionField');
        $count = $model->count();
        
        $fields = $model->order('sort_order asc, id asc')
                       ->page($page, $pageSize)
                       ->select();
        
        // 处理选择项显示
        foreach($fields as &$field) {
            if($field['field_type'] == 'select' && $field['select_options']) {
                $options = json_decode($field['select_options'], true);
                $field['select_options_display'] = is_array($options) ? implode(', ', $options) : $field['select_options'];
            } else {
                $field['select_options_display'] = '-';
            }
        }
        
        $this->assign('fields', $fields);
        $this->assign('field_types', $this->field_types);
        $this->assign('count', $count);
        $this->assign('page', $page);
        $this->assign('pageSize', $pageSize);
        $this->display();
    }

    /**
     * 添加条件字段页面
     */
    public function add(){
        $this->assign('field_types', $this->field_types);
        $this->display();
    }

    /**
     * 执行添加条件字段
     */
    public function runAdd(){
        $model = M('PremiumDiscountConditionField');
        
        $data = array(
            'field_code' => I('post.field_code'),
            'field_name' => I('post.field_name'),
            'field_type' => I('post.field_type'),
            'field_unit' => I('post.field_unit'),
            'is_range' => I('post.is_range', 0),
            'is_enabled' => I('post.is_enabled', 1),
            'sort_order' => I('post.sort_order', 100),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        );
        
        // 处理选择项
        if($data['field_type'] == 'select') {
            $select_options = I('post.select_options');
            if($select_options) {
                // 将换行分隔的选项转换为JSON数组
                $options = array_filter(array_map('trim', explode("\n", $select_options)));
                $data['select_options'] = json_encode($options, JSON_UNESCAPED_UNICODE);
            }
        }
        
        // 验证字段代码是否重复
        $exists = $model->where(array('field_code' => $data['field_code']))->find();
        if($exists) {
            die(json_encode(array('code' => 0, 'msg' => '字段代码已存在')));
        }
        
        if($model->add($data)) {
            die(json_encode(array('code' => 1, 'msg' => '添加成功')));
        } else {
            die(json_encode(array('code' => 0, 'msg' => '添加失败')));
        }
    }

    /**
     * 编辑条件字段页面
     */
    public function edit(){
        $id = I('get.id');
        $field = M('PremiumDiscountConditionField')->find($id);
        
        if(!$field) {
            $this->error('字段不存在');
        }
        
        // 处理选择项显示
        if($field['field_type'] == 'select' && $field['select_options']) {
            $options = json_decode($field['select_options'], true);
            $field['select_options_text'] = is_array($options) ? implode("\n", $options) : '';
        } else {
            $field['select_options_text'] = '';
        }
        
        $this->assign('field', $field);
        $this->assign('field_types', $this->field_types);
        $this->display();
    }

    /**
     * 执行编辑条件字段
     */
    public function runEdit(){
        $model = M('PremiumDiscountConditionField');
        $id = I('post.id');
        
        $data = array(
            'id' => $id,
            'field_code' => I('post.field_code'),
            'field_name' => I('post.field_name'),
            'field_type' => I('post.field_type'),
            'field_unit' => I('post.field_unit'),
            'is_range' => I('post.is_range', 0),
            'is_enabled' => I('post.is_enabled', 1),
            'sort_order' => I('post.sort_order', 100),
            'updated_at' => date('Y-m-d H:i:s')
        );
        
        // 处理选择项
        if($data['field_type'] == 'select') {
            $select_options = I('post.select_options');
            if($select_options) {
                // 将换行分隔的选项转换为JSON数组
                $options = array_filter(array_map('trim', explode("\n", $select_options)));
                $data['select_options'] = json_encode($options, JSON_UNESCAPED_UNICODE);
            }
        } else {
            $data['select_options'] = null;
        }
        
        // 验证字段代码是否重复（排除当前记录）
        $exists = $model->where(array('field_code' => $data['field_code'], 'id' => array('neq', $id)))->find();
        if($exists) {
            die(json_encode(array('code' => 0, 'msg' => '字段代码已存在')));
        }
        
        if($model->save($data)) {
            die(json_encode(array('code' => 1, 'msg' => '修改成功')));
        } else {
            die(json_encode(array('code' => 0, 'msg' => '修改失败')));
        }
    }

    /**
     * 删除条件字段
     */
    public function del(){
        $id = I('get.id');
        
        // 检查是否有关联的规则详情
        $detailCount = M('PremiumDiscountRuleDetail')->where(array('field_id' => $id))->count();
        if($detailCount > 0) {
            die(json_encode(array('code' => 0, 'msg' => '该字段已被规则使用，无法删除')));
        }
        
        if(M('PremiumDiscountConditionField')->delete($id)) {
            die(json_encode(array('code' => 1, 'msg' => '删除成功')));
        } else {
            die(json_encode(array('code' => 0, 'msg' => '删除失败')));
        }
    }

    /**
     * 切换字段启用状态
     */
    public function toggleStatus(){
        $id = I('get.id');
        $field = M('PremiumDiscountConditionField')->find($id);
        
        if(!$field) {
            die(json_encode(array('code' => 0, 'msg' => '字段不存在')));
        }
        
        $newStatus = $field['is_enabled'] ? 0 : 1;
        $data = array(
            'id' => $id,
            'is_enabled' => $newStatus,
            'updated_at' => date('Y-m-d H:i:s')
        );
        
        if(M('PremiumDiscountConditionField')->save($data)) {
            $statusText = $newStatus ? '启用' : '禁用';
            die(json_encode(array('code' => 1, 'msg' => $statusText . '成功')));
        } else {
            die(json_encode(array('code' => 0, 'msg' => '操作失败')));
        }
    }

    /**
     * 更新排序
     */
    public function updateSort(){
        $id = I('get.id');
        $sort_order = I('get.sort_order');
        
        $data = array(
            'id' => $id,
            'sort_order' => $sort_order,
            'updated_at' => date('Y-m-d H:i:s')
        );
        
        if(M('PremiumDiscountConditionField')->save($data)) {
            die(json_encode(array('code' => 1, 'msg' => '排序更新成功')));
        } else {
            die(json_encode(array('code' => 0, 'msg' => '排序更新失败')));
        }
    }

    /**
     * 获取启用的字段列表（AJAX接口）
     */
    public function getEnabledFields(){
        $fields = M('PremiumDiscountConditionField')
                 ->where(array('is_enabled' => 1))
                 ->order('sort_order asc, id asc')
                 ->select();
        
        die(json_encode(array('code' => 1, 'data' => $fields)));
    }
}

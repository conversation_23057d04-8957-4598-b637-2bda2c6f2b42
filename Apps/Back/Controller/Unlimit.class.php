<?php

class Unlimit{

	

	//取pid下全部子分类

	static public function getAllCat($cat, $pid=0, $level=1){

		$arr=array();

		foreach($cat as $v){

			if($v['pid']==$pid){

				$v['level'] = $level;

				$v['str'] = str_repeat('|--', $level);

				$arr[] = $v;

				$arr = array_merge($arr, self::getAllCat($cat, $v['id'], $level+1));

			}

		}

			return $arr;

	}

	

	

	//多维数组形式

	static public function getAllCatLayer($cat, $pid=0){

		$arr = array();

		foreach($cat as $v){

			if($v['pid']==$pid){

				$v['child']= self::getAllCatLayer($cat, $v['id']);

				$arr[] = $v;

				

			}

		}

		return $arr;

		

	}

	

	//字符串形式

	static public function getAllCatStr($cat,$pid=0,$level=1){

		$str ='';

		foreach($cat as $v){

			if($v['pid']==$pid){

				$str.=str_repeat('|--',$level);

				$str.= $v['name'].'<br />';

				$str.= self::getAllCatStr($cat,$v['id'],$level+1);

			}

		}

		return $str;

	}

	

	

	//取当前id的父级栏目如：breadnav

	static public function getParent($cat, $id){

			$arr = array();

			foreach ($cat as $v){

				if($v['id']==$id){

					$arr[]=$v;

					//$arr = array_merge($arr, self::getParent($cat, $v['pid']));

					//顺序倒下

					$arr = array_merge(self::getParent($cat, $v['pid']), $arr);

				}

			}

			return $arr;

	}

	

	

	//取id下所有子栏目，如查询某栏目下所有商品等

	static public function getChildsId($cat , $id){

		$arr = array();

			foreach ($cat as $v){

				if($v['pid']==$id){

					$arr[]=$v['id'];

					$arr =  array_merge($arr, self::getChildsId($cat, $v['id']));	

				}

		}

		return $arr;

		

	}

	

		

}





?>
<?php
namespace Back\Controller;
use Think\Controller;
class UserController extends CommController {
	public function _initialize(){
		parent::_initialize();
			$this->cat_name = '用户管理';
	}
	public function add(){
		$this->display();
	}

	function runCheck(){
		if(I('get.id')){
			$u = M('User')->find(I('get.id'));
			if($u['is_check'] == 0){
				M('User')->where(['id'=>$u['id']])->save(['is_check'=>1]);
			}else{
				M('User')->where(['id'=>$u['id']])->save(['is_check'=>0]);
			}

			redirect($_SERVER['HTTP_REFERER']);
		}
	}
	public function runAdd(){
		$t = M('User');
		$t->create();
		$t->order_id = 100;
		$t->add_time = time();
		$t->add();
		$this->redirect('index', array('type'=>I('post.type')));
	}
	public function index(){
		$this->user_type = C('USER_TYPE');
		$where = '`id` >0 ';
		if(I('get.key')){
			$where .= ' and  (`nickName` like "%'.I('get.key').'%" or `username` like "%'.I('get.key').'%")';
		}
		$count =  M('User')->where($where)->count();
		$page = new \Think\Page($count,10);
		$page->setConfig('theme', "%UP_PAGE% %LINK_PAGE% %DOWN_PAGE% %END% <label class='f_14 clr_6'>共%TOTAL_ROW%条</label>");
		$this->page = $page->show();
		$lists = M('User')->where($where )->order('add_time desc')->limit($page->firstRow.','.$page->listRows)->select();
		foreach($lists as $k=>$v){
			$lists[$k]['user_type_text'] =  C('USER_TYPE')[$v['user_type']];
		}
		// print_r($lists);
		$this->lists = $lists;
		$this->display();
	}

	function toExcel($list,$filename,$indexKey,$startRow=1,$excel2007=false){  
			//文件引入  
			require_once './Excel/PHPExcel.php';//包含excel入口
			require_once './Excel/PHPExcel/IOFactory.php';
			require_once './Excel/PHPExcel/Writer/Excel2007.php';

			ob_end_clean();
			if(empty($filename)) $filename = time();  
			if( !is_array($indexKey)) return false;  

			$header_arr = array('A','B','C','D','E','F','G','H','I','J','K','L','M', 'N','O','P','Q','R','S','T','U','V','W','X','Y','Z');  
			//初始化PHPExcel()  
			$objPHPExcel = new \PHPExcel();  

			//设置保存版本格式  
			if($excel2007){  
					$objWriter = new \PHPExcel_Writer_Excel2007($objPHPExcel);  
					$filename = $filename.'.xlsx';  
			}else{  
					$objWriter = new \PHPExcel_Writer_Excel5($objPHPExcel);  
					$filename = $filename.'.xls';  
			} 

			//接下来就是写数据到表格里面去  
			$objActSheet = $objPHPExcel->getActiveSheet();  


			$bian = "G";

			 // $objPHPExcel->getActiveSheet()->getRowDimension('2')->setRowHeight(22);

			//设置加粗
			$objActSheet->getStyle("A1:".$bian."1")->getFont()->setBold(true);
			//合并单元格
			// $objActSheet->mergeCells('B1:'.$bian.'1');

			//某列颜色
			// $objActSheet->getStyle($red1.'1:'.$red1.(count($list)+1))->getFont()->getColor()->setARGB(\PHPExcel_Style_Color::COLOR_RED);
			// $objActSheet->getStyle($red2.'1:'.$red2.(count($list)+1))->getFont()->getColor()->setARGB(\PHPExcel_Style_Color::COLOR_RED);
			// $objActSheet->getStyle( $huang.'1:'.$huang.(count($list)+1))->getFill()->setFillType(\PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB('ffff00');


			//设置某列文本
			$objActSheet->getStyle('A')->getNumberFormat()->setFormatCode(\PHPExcel_Style_NumberFormat::FORMAT_TEXT);
			//加边框
			$styleThinBlackBorderOutline = array(
				'borders' => array(
					'allborders' => array( //设置全部边框
							'style' => \PHPExcel_Style_Border::BORDER_THIN //粗的是thick
					),

				),
			);
			$objActSheet->getStyle( 'A1:'.$bian.(count($list)))->applyFromArray($styleThinBlackBorderOutline);

			$objActSheet->getColumnDimension('B')->setWidth(18);
			$objActSheet->getColumnDimension('D')->setWidth(18);
			$objActSheet->getColumnDimension('E')->setWidth(18);
			$objActSheet->getColumnDimension('F')->setWidth(18);

			//设置背景
			// $objActSheet->getStyle('A1:'.$bian.'1')->getFill()->setFillType(\PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB('0be5e2');

			//设置内容并换行
			// $objActSheet->setCellValue('B1',$headtext);
			// $objActSheet->getStyle('B1')->getAlignment()->setWrapText(true);

			//字体大小
			// $objActSheet->getStyle("B1")->getFont()->setBold(true)->setSize(15);
			//高度
			// $objActSheet->getRowDimension('1')->setRowHeight(50);


			// $objDrawing= new  \PHPExcel_Worksheet_Drawing();
			// $objDrawing->setPath('./Public/images/logo_excel.png');
			// $objDrawing->setHeight(70);//照片高度
			// $objDrawing->setWidth(70); //照片宽度
			// $objDrawing->setOffsetX(10);
			// // $objDrawing->setOffsetY(10);
			// $objDrawing->setCoordinates('A1');
			// $objDrawing->setWorksheet($objActSheet);

			//设置居中
			// $objPHPExcel
			// ->getActiveSheet()
			// ->getStyle('B1:'.$bian.'1')
			// ->getAlignment()
			// ->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);

			set_time_limit(0);
			ini_set("memory_limit",-1);
			$startRow = 1;  
			foreach ($list as $row) {  
					foreach ($indexKey as $key => $value){  
							//这里是设置单元格的内容  
							$objActSheet->setCellValue($header_arr[$key].$startRow,$row[$value]);
					}
					$startRow++;
			}  
		
			// 下载这个表格，在浏览器输出  
			header("Pragma: public");  
			header("Expires: 0");  
			header("Cache-Control:must-revalidate, post-check=0, pre-check=0");  
			header("Content-Type:application/force-download");  
			header("Content-Type:application/vnd.ms-execl");  
			header("Content-Type:application/octet-stream");  
			header("Content-Type:application/download");
			header('Content-Disposition:attachment;filename='.$filename.'');  
			header("Content-Transfer-Encoding:binary");  
			// $objWriter->save('php://output'); 
			
			$filename = './Attached/'.$filename;
			$objWriter->save(iconv('utf-8', 'gb2312',$filename)); 
			$ret['code'] = 1;
			$ret['content'] = '<a href="'.C('host_url').$filename.'" target="_blank">'.C('host_url').$filename.'</a>';
			$ret['msg'] = '操作成功';
			$ret['time'] = date('Y-m-d H:i:s');
			$ret['url'] = C('host_url').$filename;
			die(json_encode($ret));
	}


	public function excel(){
		
		$this->user_type = C('USER_TYPE');
		$where = '`id` >0 ';
		if(I('get.key')){
			$where .= ' and  (`nickName` like "%'.I('get.key').'%" or `username` like "%'.I('get.key').'%")';
		}
		$count =  M('User')->where($where)->count();
		$page = new \Think\Page($count,10);
		$page->setConfig('theme', "%UP_PAGE% %LINK_PAGE% %DOWN_PAGE% %END% <label class='f_14 clr_6'>共%TOTAL_ROW%条</label>");
		$this->page = $page->show();
		$lists = M('User')->where($where )->order('add_time desc')->limit($page->firstRow.','.$page->listRows)->select();
		foreach($lists as $k=>$v){
			$lists[$k]['username']  = $v['username'].' ';
			$lists[$k]['member_type'] =  $v['member_type'] == 1 ? 'VIP会员' : '普通会员';
			$lists[$k]['add_time'] = date('Y-m-d H:i', $v['add_time']);
			$lists[$k]['is_check'] = $v['is_check'] == 1 ? '已审核' : '未审核';
		}

		


		$indexKey = array('id', 'username','nickname','company','member_type','add_time','is_check');       
		//excel表头内容
		$header = array('id'=>'ID', 'username'=>'用户名','nickname'=>'姓名','company'=>'公司','member_type'=>'会员类型','add_time'=>'加入时间','is_check'=>'审核');
		array_unshift($lists,$header);//将查询到的订单数据和表头内容合并,构造成数组list

		$this->toExcel($lists,'user_'.date('Y-m-d'),$indexKey,1,true);


	}
	function delAll(){
		if(I('post.id')){
			foreach(I('post.id') as $k=>$v){
				$res = M('User')->find($v);
				//删除图片
				if($res['thumbs']!=''){
					@unlink(realpath('.').'/Attached/'.$res['thumbs']);
				}
				M('User')->delete($res['id']);
			}
			die(json_encode(array('code'=>1)));
		}
	}
	public function del(){
		$res = M('User')->find(I('get.id'));
		if($res['thumbs']!=''){
			@unlink(realpath('.').'/Attached/'.$res['thumbs']);
		}
		M('User')->delete(I('get.id'));
		die(json_encode(array('code'=>1)));
	}
	public function isRecom(){
		$res = M('User')->find(I('get.id'));
		if($res['is_recom'] == 1){
			M('User')->where(array('id'=>$res['id']))->save(array('is_recom'=>0));
		}else{
			M('User')->where(array('id'=>$res['id']))->save(array('is_recom'=>1));
		}
		redirect($_SERVER['HTTP_REFERER']);
	}
	public function edit(){
		$res = M('User')->find(I('get.id'));
		if($res['user_type'] == 2){
			$this->certificate_type = C('CERTIFICATE_JIGOU');
		}else{
			$this->certificate_type = C('CERTIFICATE_TYPE');
		}
		$res['user_type_text'] = C('USER_TYPE')[$res['user_type']];
		$this->res = $res;
		$this->display();
	}
	public function runEdit(){
		$t = M('User');
		$t->create();
		// $t->add_time = time();
		if(I('post.password')!=''){
			$t->password = md5(I('post.password'));
		}else{
			unset($t->password);
		}
		$t->save();
		die(json_encode(array('code'=>1)));
	}
    public function runOrder(){
    		M('User')->where(array('id'=>I('get.id')))->save(array('order_id'=>I('get.order_id')));
    		die(json_encode(array('code'=>1, 'msg'=>'操作成功')));
    }
}
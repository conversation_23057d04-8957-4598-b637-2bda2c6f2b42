<?php
namespace Back\Controller;
use Think\Controller;
use Vendor\Unlimit;
class XiangceController extends CommController {
	public function _initialize(){
			parent::_initialize();

			$this->laoshi = M('Art')->where(['cat_id'=>702])->field('id, title')->order('order_id desc, id desc')->select();

			$this->jieduan = M('Cat')->where(['pid'=>0])->order('order_id desc, id asc')->limit(3)->select();

			$this->ke_type = M('Cat')->where(['pid'=>305])->order('order_id desc, id asc')->select();
	}


	function getClassType(){
		if(I('get.id')){
			$lists = M('Art')->where(['ke_type_id'=>I('get.id')])->order('order_id desc, id asc')->select();
			foreach($lists as $k=>$v){
				$lists[$k]['jieduan_id_text'] = M('Cat')->where(['id'=>$v['jieduan_id']])->getField('name');
				$lists[$k]['ke_type_id_text'] = M('Cat')->where(['id'=>$v['ke_type_id']])->getField('name');
				$lists[$k]['nianji_id_text'] = M('Cat')->where(['id'=>$v['nianji_id']])->getField('name');
				$lists[$k]['kemu_id_text'] = M('Cat')->where(['id'=>$v['kemu_id']])->getField('name');
			}
			die(json_encode($lists));
		}
	}


	function getChild(){
		if(I('get.id')){
			$lists = M('Cat')->where(['pid'=>I('get.id')])->order('order_id desc, id asc')->select();
			die(json_encode($lists));
		}
		
	}

	function dagangD(){
		if(I('get.id')){
			M('Dagang')->delete(I('get.id'));
			die(json_encode(['code'=>1]));
		}
	}


	function runDagangE(){
		// print_r($_POST);
		$d = M('Dagang');
		$d->create();
		if(I('post.bt')){
			$bt = I('post.bt');
			$summ = I('post.summ');
			$content = '';
			foreach($bt as $k=>$v){
				$content .= $bt[$k].'|'.$summ[$k].'@';
			}
			$d->content = substr($content, 0, -1);
		}
		
		$d->add_time = time();
		$d->save();
		die(json_encode(array('code'=>1,'msg'=>'操作成功')));

	}


	function dagangE(){
		$res = M('Dagang')->find(I('get.id'));
		$cont_lists = explode('@', $res['content']);
		foreach ($cont_lists as $k => $v) {
			$cont_lists[$k] = explode('|',$v);

		}

		$res['cont_lists'] = $cont_lists;
		// print_r($res);
		$this->res = $res;
		$this->display();
	}


	function runDagangA(){
		// print_r($_POST);
		$d = M('Dagang');
		$d->create();
		if(I('post.bt')){
			$bt = I('post.bt');
			$summ = I('post.summ');
			$content = '';
			foreach($bt as $k=>$v){
				$content .= $bt[$k].'|'.$summ[$k].'@';
			}
			$d->content = substr($content, 0, -1);
		}
		
		$d->add_time = time();
		$d->add();
		die(json_encode(array('code'=>1,'msg'=>'操作成功')));

	}

	function dagangA(){
		$this->display();
	}


	function dagang(){
		$this->lists = M('Dagang')->where(['ke_id'=>I("get.id")])->order('id asc')->select();
		$this->display();
	}

	public function index(){


		$where = '`cat_id` = '.I('get.cid');
		if(I('get.jieduan_id')){
			$where.=' and `jieduan_id` = '.I('get.jieduan_id');

			//当前阶段的全部年级
			$this->all_nianji = M('Cat')->where(['pid'=>I('get.jieduan_id')])->order('order_id desc, id asc')->select();
		}
		if(I('get.nianji_id')){
			$where.=' and `nianji_id` = '.I('get.nianji_id');

			//当前年级的全部科目
			$this->all_kemu = M('Cat')->where(['pid'=>I('get.nianji_id')])->order('order_id desc, id asc')->select();
		}
		if(I('get.kemu_id')){
			$where.=' and `kemu_id` = '.I('get.kemu_id');
		}
		if(I('get.ke_type_id')){
			$where.=' and `ke_type_id` = '.I('get.ke_type_id');
		}
		if(I('get.key')){
			$where.=' and `title` like "%'.I('get.key').'%"';
		}

		// print_r($where);
		$count =  M('Xiangce')->where($where)->count();
		$page = new \Think\Page($count,10);
		$page->setConfig('theme', "%UP_PAGE% %LINK_PAGE% %DOWN_PAGE% %END% <label class='f_14 clr_6'>共%TOTAL_ROW%条</label>");
		$this->page = $page->show();
		$this->lists = M('Xiangce')->where($where)->order('order_id asc,id desc')->limit($page->firstRow.','.$page->listRows)->select();
		// print_r(M('Xiangce')->_sql());
		$this->display();
	}
	public function add(){
		$this->link_id = time();
		$this->display();
	}
	public function runAdd(){

		$a = M('Xiangce');
		if($a->create()){
				$a->add_time = I('post.pub_date') == '' ? time() :  strtotime(I('post.pub_date'));
				$a->baoming_end_time =strtotime(I('post.baoming_end_time'));
				//$a->pub_date = strtotime(I('post.pub_date'));
				$a->content = $_POST['content'];
				$a->content_en = $_POST['content_en'];
				$a->act_start_date = strtotime(I('post.act_start_date'));
				$a->act_end_date = strtotime(I('post.act_end_date'));
				$a->baoming_end_date = strtotime(I('post.baoming_end_date'));

				$class_set = M('Art')->find(I('post.class_set'));
				$a->jieduan_id = $class_set['jieduan_id'];
				$a->nianji_id = $class_set['nianji_id'];
				$a->kemu_id = $class_set['kemu_id'];
				$a->ke_type_id = $class_set['ke_type_id'];
				$a->class_set = $class_set['id'];

			
				if(I('post.laoshi_ids')){
					$a->laoshi_ids = I('post.laoshi_ids');
					//老师关键词
					$temp = M('Art')->where('`id` in('.I('post.laoshi_ids').')')->field('title')->select();
					foreach($temp  as $k=>$v){
						$temp2[]=$v['title'];
					}
					$a->laoshi_keywords = implode(',', $temp2);
				}
				$a->order_id = 100;
				if($a->add()){
					//$this->show($a->_sql());
					die(json_encode(array('code'=>1)));
				}
		}
	}
	function picDel(){
		 M('Xiangce_pic')->delete(I('get.id'));
		 $ret_lists = M('Xiangce_pic')->where(array('link_id'=>$_GET['linkid']))->order('id asc')->select();
		 // print_r(M('Xiangce_pic')->_sql());
		 $retArr = array(
			'code'=> 1,
			'ret_lists' => $ret_lists
		);
		 echo(json_encode($retArr));
	}
	public function edit(){
		$this->res = $res = M('Xiangce')->find(I('get.id'));
		$this->link_id = $res['link_id'];
		$this->xiangce = M('Xiangce_pic')->where(array('link_id'=>$res['link_id']))->order('id asc')->select();

		$nianji = M('Cat')->where(['id'=>$res['nianji_id']])->find();
		$this->nianji = M('Cat')->where(['pid'=>$nianji['pid']])->order('order_id desc, id asc')->select();

		$kemu = M('Cat')->where(['id'=>$res['kemu_id']])->find();
		$this->kemu = M('Cat')->where(['pid'=>$kemu['pid']])->order('order_id desc, id asc')->select();


		$banji= M('Art')->where(['ke_type_id'=>$res['ke_type_id']])->order('order_id desc, id asc')->select();

		foreach($banji as $k=>$v){
			$banji[$k]['jieduan_id_text'] = M('Cat')->where(['id'=>$v['jieduan_id']])->getField('name');
			$banji[$k]['ke_type_id_text'] = M('Cat')->where(['id'=>$v['ke_type_id']])->getField('name');
			$banji[$k]['nianji_id_text'] = M('Cat')->where(['id'=>$v['nianji_id']])->getField('name');
			$banji[$k]['kemu_id_text'] = M('Cat')->where(['id'=>$v['kemu_id']])->getField('name');
		}

		$this->banji = $banji;

		$this->display();
	}
	public function runEdit(){

		$a = M('Xiangce');
		if($a->create()){
			$a->add_time =I('post.pub_date') == '' ? time() :  strtotime(I('post.pub_date'));
			$a->baoming_end_time =strtotime(I('post.baoming_end_time'));
			//$a->pub_date = strtotime(I('post.pub_date'));
			$a->content = $_POST['content'];
			$a->content_en = $_POST['content_en'];
			$a->act_start_date = strtotime(I('post.act_start_date'));
			$a->act_end_date = strtotime(I('post.act_end_date'));
			$a->baoming_end_date = strtotime(I('post.baoming_end_date'));

			$class_set = M('Art')->find(I('post.class_set'));
			$a->jieduan_id = $class_set['jieduan_id'];
			$a->nianji_id = $class_set['nianji_id'];
			$a->kemu_id = $class_set['kemu_id'];
			$a->ke_type_id = $class_set['ke_type_id'];
			$a->class_set = $class_set['id'];

			if(I('post.laoshi_ids')){
				$a->laoshi_ids =I('post.laoshi_ids');
				//老师关键词
			
				$temp = M('Art')->where('`id` in('.I('post.laoshi_ids').')')->field('title')->select();
				foreach($temp  as $k=>$v){
					$temp2[]=$v['title'];
				}
				$a->laoshi_keywords = implode(',', $temp2);
			}
			$a->save();
			// $this->redirect('index', array('cid'=>I('post.cid'),'type'=>I('post.type')));
			die(json_encode(array('code'=>1)));
		}
	}
	public function del(){
		$res = M('Xiangce')->find(I('get.id'));
		//删除图片
		if($res['thumbs']!=''){
			@unlink(realpath('.').'/Attached/'.$res['thumbs']);
		}
		if(M('Xiangce')->delete(I('get.id'))){
			die(json_encode(array('code'=>1)));
		}
	}
	public function runOrder(){
		M('Xiangce')->where(array('id'=>I('get.id')))->save(array('order_id'=>I('get.order_id')));
		die(json_encode(array('code'=>1, 'msg'=>'操作成功')));
	}
	public function runRecom(){
		$res = M('Xiangce')->find(I('get.id'));
		if($res['is_recom']==1){
			M('Xiangce')->where(array('id'=>$res['id']))->save(array('is_recom'=>0));
		}else{
			M('Xiangce')->where(array('id'=>$res['id']))->save(array('is_recom'=>1));
		}
		// die(json_encode(array('code'=>1, 'msg'=>'操作成功')));
		redirect($_SERVER['HTTP_REFERER']);
	}

	function runHot(){
		$res = M('Xiangce')->find(I('get.id'));
		if($res['is_hot']==1){
			M('Xiangce')->where(array('id'=>$res['id']))->save(array('is_hot'=>0));
		}else{
			M('Xiangce')->where(array('id'=>$res['id']))->save(array('is_hot'=>1));
		}
		redirect($_SERVER['HTTP_REFERER']);
	}

	

}
<!DOCTYPE html>


<html lang="zh-CN">


<head>


<include file="Public:header" />


</head>


<body>


<div class="cat_list">


	<include file="Public:top" />


	


	<div class="mt_10 f_14 clr_6">


		


		<table class="cat_tbl f_14 clr_3" style="width:800px;">


			<tr><th>编号</th><th>用户名</th><th>修改密码</th><th>操作</th></tr>


			<foreach name="lists" item="v" >


				<tr>


					<td>{$v.id}</td>


					<td>{$v.name}</td>


					<td><input placeholder="修改密码" myid="{$v.id}" class="ipt_default ipt_pwd_blur" style="width:200px;" type="password" /></td>


					<td><a href="{:U('del', array('id'=>$v['id']))}" class="f_14 clr_6" onclick="return confirm('确认删除？');">[删除]</a></td>


				</tr>	


			</foreach>	


		</table>


		


		<br /><br />


	<form id="fm1" action="{:U('runAdd')}" method="post">


		<input type="hidden" type="hidden" name="type" value="1" />


		用户名：</td><td><input id="name" name="name"  class="ipt_default pad_5 clr_6"/>


		密码：</td><td><input id="pwd" type="password" name="pwd"  class="ipt_default pad_5 clr_6"/> 


		<input type="submit" value=" 添加用户 " class="sub_default" />


	</form>


	</div>


	


	<script>


		$('.ipt_pwd_blur').blur(function(){


			if($(this).val()!=''){


				$.ajax({


					url:'{:U("runPwd")}',	


					data:{id:$(this).attr('myid'), pwd:$(this).val()},


					dataType:'json',


					type:'post',


					success:function(d){


						alert(d.msg);	


						history.go(0);	


					}


				});	


			}	


		});


		


		


		$(function() {


		 $("#fm1").validate({


			rules: {


				name: {


					required:true


				},


				pwd: {


					required:true


				}


			  },


		        messages: {


				   	name: {


				   		required:' 必填'


				   	},


				   pwd: {


				   		required:' 必填'


				   	},


			 }


		    });


		});





	</script>


</div>


</body>


</html>
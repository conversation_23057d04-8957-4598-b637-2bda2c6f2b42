<!DOCTYPE html>
<html lang="zh-CN">
<head>
	<include file="Public:header" />
	<script charset="utf-8" src="__PUBLIC__/js/jquery.form.js"></script>
	<link rel="stylesheet" href="__PUBLIC__/kinder/themes/default/default.css" />
	<link rel="stylesheet" href="__PUBLIC__/kinder/plugins/code/prettify.css" />
	<script charset="utf-8" src="__PUBLIC__/kinder/kindeditor-min.js"></script>
	<script charset="utf-8" src="__PUBLIC__/kinder/lang/zh_CN.js"></script>
	<script charset="utf-8" src="__PUBLIC__/kinder/plugins/code/prettify.js"></script>
	<script type="text/javascript" src="__PUBLIC__/js/My97DatePicker/WdatePicker.js"></script>
	<script>
		KindEditor.ready(function(K) {
			var editor1 = K.create('textarea[class="htmltext"]', {
				cssPath : '__PUBLIC__/kinder/plugins/code/prettify.css',
				uploadJson : '__PUBLIC__/kinder/upload_json.php',
				fileManagerJson : '__PUBLIC__/kinder/file_manager_json.php',
				allowFileManager : true,
				urlType:'domain',
			afterBlur: function () { this.sync(); },//注意这里（异步提交时需要同步）
			afterCreate: function () {
				var self = this;
				self.sync();//把富文本编辑器的内容放到 文本域里面去。
			}
		});
			prettyPrint();
		});
		$(function(){
	//上传
	$('#uploadForm').ajaxForm({
		dataType: 'json',
		success: function(data){
			if(data.up_msg=='OK'){
				data.up_msg = '上传成功！';
				$('#img_wrap').html('<img src="__ROOT__/Attached/'+data.img_url+'" style="width:80px;height:80px;" />');
				$('#thumbs').val(data.img_url);
			}
			$('.tips').text(data.up_msg);
		},
		beforeSubmit:function(){
		}
	});
	$('.go_up').click(function(){
		$('#thumbs_hide').click();
	});
	$('#thumbs_hide').change(function(){
		$('#uploadForm').submit();
	});
			//上传2
			$('#uploadForm2').ajaxForm({
				dataType: 'json',
				success: function(data){
					if(data.up_msg=='OK'){
						data.up_msg = '上传成功！';
						$('#img_wrap2').html('<img src="__ROOT__/Attached/'+data.img_url+'" style="width:80px;height:80px;" />');
						$('#thumbs2').val(data.img_url);
					}
					$('.tips2').text(data.up_msg);
				},
				beforeSubmit:function(){
				}
			});
			$('.go_up2').click(function(){
				$('#thumbs_hide2').click();
			});
			$('#thumbs_hide2').change(function(){
				$('#uploadForm2').submit();
			});
		//   //验证
		// $("#fm1").validate({
		// 	rules: {
		// 		title: "required"
		// 	  },
		// 	   errorPlacement: function(error, element) {
		// 	 		error.addClass('alert_tips');
		// 			element.after('<span class="pos_rela alert_rela ver_mid"></span>');
		// 			element.siblings('.alert_rela').append(error);
		// 			error.width(error.html().length*12);
		// 	}
		//   });
	});
</script>
</head>
<div class="x-body">
	<form  class="layui-form" method="post" id="fm1">
		<input type="hidden" name="id" value="{$res['id']}" />
		<input type="hidden" name="type" value="{$_GET['type']}" />
		<input type="hidden" name="cat_id" value="{$_GET['cid']}" />
		<table class="tbl3 bg_f w_100 rad_10 mt_10 clr_79">
			<tr><td  style="width:100px;">姓名</td><td><input name="title" class="layui-input" lay-verify="required"  value="{$res.title}" placeholder="必填"  /></td></tr>
			<tr><td style="width:100px;">描述</td><td><textarea name="summary" class="layui-textarea">{$res.summary}</textarea></td></tr>
			<tr><td style="width:100px;">毕业院校</td><td><input lay-verify="required"  name="biyeyuanxiao" class="layui-input" value="{$res.biyeyuanxiao}" placeholder="必填"  /></td></tr>
			<tr><td style="width:100px;">学历</td><td><input lay-verify="required"  name="xueli" class="layui-input" value="{$res.xueli}" placeholder="必填"  /></td></tr>
			<tr><td style="width:100px;">教龄</td><td><input lay-verify="required"  name="jiaoling" class="layui-input" value="{$res.jiaoling}" placeholder="必填"  /></td></tr>
			<tr><td style="width:100px;">职务</td><td><input lay-verify="required"  name="zhiwu" class="layui-input" value="{$res.zhiwu}" placeholder="必填"  /></td></tr>
			<tr><td >缩略图</td><td>
				<span id="img_wrap">
					<if condition="$res.thumbs neq ''"><img src="__ROOT__/Attached/{$res.thumbs}" style="width:80px;height:80px;" /></if>
				</span>
				<span>
					<input name="thumbs" id="thumbs" type="hidden"  value="{$res.thumbs}" />
					<input type="button" class="go_up layui-btn" value="上传"  />
					<span class="tips">(2M以内，jpg/png，200*200)</span>
				</span>
			</td></tr>
			<!--
				<tr><td >名片二维码</td><td>
					<span id="img_wrap2">
						<if condition="$res.thumbs2 neq ''"><img src="__ROOT__/Attached/{$res.thumbs2}" style="width:80px;height:80px;" /></if>
					</span>
					<span>
						<input name="thumbs2" id="thumbs2" type="hidden"  value="{$res.thumbs2}" />
						<input type="button" class="go_up2 layui-btn" value="上传"  />
						<span class="tips2">2M以内、jpg/png </span>
					</span>
				</td></tr> -->
			<tr ><td>教师资质</td><td><textarea class="htmltext w_100" name="jszz" style="height:500px;visibility:hidden;" >{$res.jszz}</textarea></td></tr>
			<tr ><td>教学经历</td><td><textarea class="htmltext w_100" name="jxjl" style="height:500px;visibility:hidden;" >{$res.jxjl}</textarea></td></tr>
			<tr ><td>教学成果</td><td><textarea class="htmltext w_100" name="jxcg" style="height:500px;visibility:hidden;" >{$res.jxcg}</textarea></td></tr>
			<tr ><td>教学特点</td><td><textarea class="htmltext w_100" name="jxtd" style="height:500px;visibility:hidden;" >{$res.jxtd}</textarea></td></tr>
			<tr><td></td><td><input type="submit" value="保存" lay-submit lay-filter="formDemo" class="layui-btn" /></td></tr>
		</table>
			</form>
			<form style="display: none;"  id="uploadForm" enctype="multipart/form-data"  method="post" action="{:U('Tools/runUpload')}">
				<input type="file" name="thumbs_hide" id="thumbs_hide" />
			</form>
			<form style="display: none;"  id="uploadForm2" enctype="multipart/form-data"  method="post" action="{:U('Tools/runUpload')}">
				<input type="file" name="thumbs_hide" id="thumbs_hide2" />
			</form>
		</div>
		<script>
			layui.use('laydate', function(){
				var laydate = layui.laydate;
	//执行一个laydate实例
	laydate.render({
		elem: '#summary' //指定元素
	});
});
			layui.use('form', function(){
				var form = layui.form;
	//监听提交
	form.on('submit(formDemo)', function(data){
		// $.post("{:U('runEdit')}",data.field, function(d){
			$.post("{:U('runEdit')}",$('#fm1').serialize(), function(d){
				if(d.code == 1){
					layer.alert("保存成功", {icon: 6},function () {
						window.parent.location.reload();
						var index = parent.layer.getFrameIndex(window.name);
									//关闭当前frame
									parent.layer.close(index);
								});
				}
			},'json');
			return false;
		});
});
</script>
</body>
</html>
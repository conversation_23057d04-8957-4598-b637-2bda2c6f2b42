<!DOCTYPE html>

<html lang="zh-CN">

<head>

	<include file="Public:header" />

</head>

<body>
<div class="x-body">

		<table  class="tbl1 bg_f w_100 rad_10 mt_10 clr_79 w_100 clr_79" >
		 <thead>
			<tr class="al_lt f_14">

				<th>文件名</th>
				<th>备份时间</th>
				<th>文件大小</th>
				<th>操作</th>
			</tr>
		 </thead>
			<notempty  name="lists">

				<foreach name="lists" item="row"  key="key" >
					<if condition="$key gt 1">
						<tr>

							<td ><a  href="{:U('Bak/index',array('Action'=>'download','file'=>$row))}" class="f_14 clr_79">{$row}</a></td>
							<td class="f_14">{$row|getfiletime=$datadir}</td>
							<td class="f_14">{$row|getfilesize=$datadir}</td>
							<td class="f_14">
								<a class=" bg_g block_a" 
									href="{:U('Bak/index',array('Action'=>'download','file'=>$row))}" title="下载" ><i class="fa fa-download" aria-hidden="true" style="color:#fff;"></i></a>
								<a class=" bg_b block_a" onclick="return confirm('确定将数据库还原到当前备份吗？')" 
									href="{:U('Bak/index',array('Action'=>'RL','File'=>$row))}" title="还原"><i class="fa fa-database" aria-hidden="true" style="color:#fff;"></i></a>
								<a class=" bg_r block_a" onclick="return confirm('确定删除该备份文件吗？')" 
									href="{:U('Bak/index',array('Action'=>'Del','File'=>$row))}"  title="删除"><i class="fa fa-trash-o" aria-hidden="true" style="color:#fff;"></i></a>
							</td>
						</tr>
					</if>
				</foreach>
				<else/>
				<tr>
					<td colspan="7">没有找到相关数据。</td>
				</tr>

			</notempty>

		</table>

		<br/>
		<p>     
			<a class="layui-btn"  href="{:U('index', array('Action'=>'backup'))}" onclick="return confirm('确认备份数据库？');"> <i class="fa fa-plus-circle" aria-hidden="true"></i> 添加备份</a>
		</p>


		<br />
		<div class="pager f_14 clr_3">
			{$page}	
		</div>
</div>

</body>

</html>
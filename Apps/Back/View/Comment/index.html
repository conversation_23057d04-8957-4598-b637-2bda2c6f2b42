<!DOCTYPE html>

<html lang="zh-CN">

<head>

<include file="Public:header" />

</head>



	<div  class="x-body">

		  <!-- <button id="add_click" class="layui-btn" ><i class="layui-icon"></i>添加</button> -->

		 <script>

		 	$('#add_click').click(function(){

		 		var aa = "{:U('add', array('cid'=>$_GET['cid'], 'type'=>$_GET['type']))}";

		 		x_admin_show('添加内容', aa);

		 	});

		 </script>



		 <foreach name="cat_small" item="v">

		 	<if condition="$_GET['small'] eq $v">

		 		 &nbsp;&nbsp; <a href="{:U('index', array('cid'=>$_GET['cid'], 'type'=>$_GET['type'], 'small'=>$v))}" style="color:red;">{$v}</a>

		 	<else />

		 		 &nbsp;&nbsp; <a href="{:U('index', array('cid'=>$_GET['cid'], 'type'=>$_GET['type'], 'small'=>$v))}">{$v}</a>

		 	</if>

		 	

		 </foreach>



<!-- 

		 <div style="float:right;">

				<form action="{:U('index')}" id="fms">

					<input name="cid" value="{$_GET['cid']}" type="hidden" />

					<input lay-verify="required"  name="key" id="key" placeholder="输入关键词" class="layui-input" style="width:200px;float: left;" />

					<input type="submit" id="search_click" value="搜索" class="layui-btn" style="float: left;" />

					<div style="clear: both;"></div>

				</form>



				<script>

					$('#search_click').click(function(){

						if($('#key').val()==''){

							alert('请输入关键词');

							$('#key').focus();

							return false;

						}

					});

				</script>

			</div> -->

		

		<div style="clear: both;"></div>

		 



		<table class="layui-table">

			<thead>

				<tr>

			

					<th>内容</th>

					<th>加入时间</th>

					<th>排序</th>

					<th>操作</th>

				</tr>	

			</thead>

			<tbody>

				<foreach name="lists" item="v">

				<tr>



					<td>{$v.content}</td>

					<td>{$v.add_time|date="Y-m-d H:i",###}</td>

					<td><input class="layui-input order_blur"  value="{$v.order_id}" myid="{$v.id}" style="width:50px;"/></td>

					<td>

						<!-- <a href="javascript:;" my_url="{:U('edit', array('id'=>$v['id'], 'cid'=>$v['cat_id'], 'type'=>$_GET['type']))}" class="edit_item" title="编辑" >

							<i class="layui-icon">&#xe642;</i>

						</a> -->

						<a href="javascript:;" my_url="{:U('del', array('id'=>$v['id']))}"  class="del_item" title="删除" >

							<i class="layui-icon">&#xe640;</i>

						</a>

						

					</td>

				</tr>

				</foreach>

			</tbody>

		</table>

		

		<div class="pager mt_10">

			{$page}

		</div>

	</div>









<script>

	$('.edit_item').click(function(){

		x_admin_show('编辑',$(this).attr('my_url'));

	});



	$('.del_item').click(function(){

		var aa = $(this);

		layer.confirm('确认删除？', {icon: 3, title:'提示'}, function(index){

			$.getJSON(aa.attr('my_url'), function(d){

				if(d.code==1){

					history.go(0);

				}

			});



			layer.close(index);

		});

	});





	$('.recom_click').click(function(){

		$.getJSON('{:U("runRecom")}',{id:$(this).attr('myid')}, function(d){if(d.code==1)history.go(0);});

	});	

	

	$('.recom_click2').click(function(){

		$.getJSON('{:U("runRecom2")}',{id:$(this).attr('myid')}, function(d){if(d.code==1)history.go(0);});

	});	

	

	$('.order_blur').blur(function(){

		$.getJSON('{:U("runOrder")}',{id:$(this).attr('myid'), order_id:$(this).val()}, function(d){if(d.code==1)history.go(0);});

	});	

</script>



</body>

</html>




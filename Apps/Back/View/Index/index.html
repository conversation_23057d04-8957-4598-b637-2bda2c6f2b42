<!doctype html>

<html lang="en">

<head>

<include file="Public:header" />

<style>



        body{background:radial-gradient(200% 100% at bottom center,#0070aa,#0b2570,#000035,#000);background:radial-gradient(220% 105% at top center,#000 10%,#000035 40%,#0b2570 65%,#0070aa);background-attachment:fixed;overflow:hidden;



        }

        #particles-js {position: absolute;width: 100%;height: 100%;}





        .holderf::-webkit-input-placeholder{

	    color: #FFF;

	}



</style>

</head>

<!-- <body class="login-bg"> -->

<body>

<div   id="particles-js" ></div>

    

    <div class="login" style="position: relative;z-index: 999;background: rgba(255,255,255,0.2);" >

        <div class="message" style="background: #1e9fff;">管理登录</div>

        <div id="darkbannerwrap"></div>

        

        <form method="post" class="layui-form" >

            <input name="username" placeholder="用户名"  type="text" lay-verify="required" class="layui-input holderf" style="background: rgba(255,255,255,0.5);color:#fff;" >

            <hr class="hr15" style="background: none;">

            <input name="password" lay-verify="required" placeholder="密码"  type="password" class="layui-input holderf" style="background: rgba(255,255,255,0.5);color:#fff;" >

             <hr class="hr15" style="background: none;">

<!--             <input name="captcha" placeholder="验证码"  type="text" lay-verify="required" class="layui-input holderf" style="width:195px;display: inline;background: rgba(255,255,255,0.5);color:#fff;" >

            <img id="verifyImg" src="{:U('Tools/verify')}"  style="height:50px;width:140px;"/>

             <hr class="hr15" style="background: none;">
 -->
            <input value="登录" lay-submit lay-filter="login" style="width:100%;background: #1e9fff;" type="submit">

        </form>

    </div>



<script src="__PUBLIC__/js/particles.min.js"></script>

<script type="text/javascript">

particlesJS("particles-js",{"particles":{"number":{"value":100,"density":{"enable":true,"value_area":800}},"color":{"value":"#ffffff"},"opacity":{"value":0.2,"random":false,"anim":{"enable":false,"speed":1,"opacity_min":0.1,"sync":false}},"size":{"value":3,"random":true,"anim":{"enable":false,"speed":40,"size_min":0.1,"sync":false}},"line_linked":{"enable":true,"distance":150,"color":"#ffffff","opacity":0.4,"width":1},"move":{"enable":true,"speed":6,"direction":"none","random":false,"straight":false,"out_mode":"out","bounce":false,"attract":{"enable":false,"rotateX":600,"rotateY":1200}}},"interactivity":{"detect_on":"canvas","events":{"onhover":{"enable":true,"mode":"grab"},"onclick":{"enable":true,"mode":"push"},"resize":true},"modes":{"grab":{"distance":140,"line_linked":{"opacity":1}},"bubble":{"distance":400,"size":40,"duration":2,"opacity":8,"speed":3},"repulse":{"distance":200,"duration":0.4},"push":{"particles_nb":4},"remove":{"particles_nb":2}}},"retina_detect":true});

</script>



    <script>

        $(function  () {

            layui.use('form', function(){

              var form = layui.form;

              // layer.msg('玩命卖萌中', function(){

              //   //关闭后的操作

              //   });

              //监听提交

              form.on('submit(login)', function(data){

         // console.log(data);

         // 

                   $.post("{:U('runLogin')}",data.field, function(d){

                    console.log(d);

                      if(d.code == 1){

                         console.log('>>>'+d.url);

                          location.href=d.url;

                          console.log('>>22>'+d.url);

                          return false;

                      }else{

                          layer.msg(d.msg);

                          $('#verifyImg').click();

                      }

                        

                    },'json');



                return false;

              });

            });







            $('#verifyImg').click(function(){

              this.src="{:U('Tools/verify')}"+"?"+Math.random();

            });

        })



        

    </script>

    <!-- 底部结束 -->

</body>

</html>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
<include file="Public:header" />
	<script charset="utf-8" src="__PUBLIC__/js/jquery.form.js"></script>
<link rel="stylesheet" href="__PUBLIC__/kinder/themes/default/default.css" />
<link rel="stylesheet" href="__PUBLIC__/kinder/plugins/code/prettify.css" />
<script charset="utf-8" src="__PUBLIC__/kinder/kindeditor-min.js"></script>
<script charset="utf-8" src="__PUBLIC__/kinder/lang/zh_CN.js"></script>
<script charset="utf-8" src="__PUBLIC__/kinder/plugins/code/prettify.js"></script>
<script>
	KindEditor.ready(function(K) {
		var editor1 = K.create('textarea[name="content"]', {
			cssPath : '__PUBLIC__/kinder/plugins/code/prettify.css',
			uploadJson : '__PUBLIC__/kinder/upload_json.php',
			fileManagerJson : '__PUBLIC__/kinder/file_manager_json.php',
			allowFileManager : true,
			afterCreate : function() {
				var self = this;
			}
		});
		prettyPrint();
	});
	</script>
</head>
<body>
	<div  class="x-body">
		<form id="fm1" class="layui-form" method="post">
			<table class="layui-table">
				<tr><td width="100">名称：</td><td><input name="name" class="layui-input" lay-verify="required"/> </td></tr>
				<!-- <tr><td width="100">名称en：</td><td><input name="name_en" class="layui-input" lay-verify="required"/> </td></tr> -->
				<tr style="display: none;"><td>是否内容页：</td><td>
					<div  id="let_show">
						<label ><input name="type" type="radio" value="1" class="ver_mid" /> <span class="ver_mid">是</span></label>
						&nbsp;&nbsp;
						<label ><input name="type" type="radio" value="0" class="ver_mid" checked="true" /> <span class="ver_mid">否</span></label>
					</div>
				</td></tr>
				<if condition="$res.type neq 0">
					<tr id="show_cat_content" >
						<td>栏目内容：</td>
						<td>
							<textarea name="content" style="width:800px;height:500px;visibility:hidden;" >{$res.content}</textarea>
						</td>
					</tr>
				</if>
				<tr><td></td><td><input type="submit" value="提交" lay-submit lay-filter="formDemo" class="layui-btn" /></td></tr>
			</table>
			<input name="pid" value="{$_GET['id']}" type="hidden" />
		</form>
	</div>
	<script>
		$(function() {
			$('#let_show').click(function(){
				if($(this).find('input').prop('checked')){
						$('#show_cat_content').show();
				}else{
					$('#show_cat_content').hide();
				}
			});
		  layui.use('form', function(){
			  var form = layui.form;
			  //监听提交
			  form.on('submit(formDemo)', function(data){
			    $.post("{:U('runAdd')}",data.field, function(d){
			    	if(d.code == 1){
			    		layer.alert("添加成功", {icon: 6},function () {
			    			window.parent.location.reload();
			    				var index = parent.layer.getFrameIndex(window.name);
				                //关闭当前frame
				                parent.layer.close(index);
			            });
			    	}
			    },'json');
			      return false;
			  });
			});
		});
	</script>
</body>
</html>
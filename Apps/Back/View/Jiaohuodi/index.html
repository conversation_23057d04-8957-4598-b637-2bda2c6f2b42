<!DOCTYPE html>
<html lang="zh-CN">
<head>
	<include file="Public:header" />
</head>
<body>
	<div   class="x-body">
		<button id="add_click" class="layui-btn" ><i class="layui-icon"></i>添加</button>
		<script>
			$('#add_click').click(function(){
				var aa = "{:U('add', array('cid'=>$_GET['cid'], 'type'=>$_GET['type']))}";
				x_admin_show('添加内容', aa);
			});
		</script>

<!-- 		<div>
			<form id="fm1" >
				<input type="" name="key" id="key" placeholder="输入名称查询ID" class="layui-input dis_ib ver_mid"  style="width:200px;" />
				<input type="submit" value="搜索" class="layui-btn dis_ib ver_mid" />
				<span id="id_span"></span>
			</form>

			<script type="text/javascript">
				$('#fm1').submit(function(e){
					e.preventDefault();
					if($('#key').val()==''){
						alert('请输入名称');
						return false;
					}

					$.getJSON('{:(U("searchID"))}', {key:$('#key').val()}, function(d){
						console.log(d)
						$('#id_span').text('ID：'+d.id);
					});
				});
			</script>
		</div> -->

		<table  class="tbl2 bg_f w_100 rad_10 mt_10 clr_79">
			<thead>
				<tr>
					<th class="al_lt">名称</th>
					<th class="al_lt">排序</th>
					<th class="al_lt">操作</th>
				</tr>
			</thead>
			<tbody>
				<foreach name="allCat" item="v">
					<if condition="$v['id'] neq 186 and $v['pid'] neq 186">
						<tr>
							<td>{$v.str}
								<a href="#." my_url="{:U('edit',array('id'=>$v['id']))}" class="edit_item <if condition='$v.pid eq 0'>clr_r</if>"  title="编辑" style="font-size:14px;">
									{$v.name}
								</a>
								（ID：{$v.id}）
							</td>
							<td>
								<input class="layui-input order_blur" myid="{$v.id}" value="{$v.order_id}" style="width:50px;" />
								<input name="id[]" value="{$v.id}" type="hidden" />
							</td>
							<td>
								<a href="javascript:;" my_url="{:U('add',array('id'=>$v['id']))}" class="add_item" title="添加" ><i class="layui-icon">&#xe654;</i></a>
								<a href="javascript:;" my_url="{:U('edit',array('id'=>$v['id']))}" class="edit_item" title="编辑" ><i class="layui-icon">&#xe642;</i></a>
								<a href="javascript:;" my_url="{:U('del', array('id'=>$v['id']))}" class="del_item" title="删除" ><i class="layui-icon">&#xe640;</i></a>
							</td>
						</tr>
					</if>
				</foreach>
			</tbody>
		</table>
		<script>
			$('.add_item').click(function(){
				x_admin_show('添加',$(this).attr('my_url'));
			});
			$('.edit_item').click(function(){
				x_admin_show('编辑',$(this).attr('my_url'));
			});
			$('.del_item').click(function(){
				var aa = $(this);
				layer.confirm('确认删除？', {icon: 3, title:'提示'}, function(index){
					$.getJSON(aa.attr('my_url'), function(d){
						if(d.code==1){
							history.go(0);
						}else{
							alert(d.msg);
						}
					});
					layer.close(index);
				});
			});
			$('.order_blur').blur(function(){
				if($(this).val()!=''){
					$.getJSON('{:U("runOrder")}', {id:$(this).attr('myid'), order_id:$(this).val()}, function(d){
						if(d.code==1){
							history.go(0);
						}
					});
				}
			});
		</script>
	</div>
</body>
</html>
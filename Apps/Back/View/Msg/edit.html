<!DOCTYPE html>

<html lang="zh-CN">

<head>

<include file="Public:header" />

<script charset="utf-8" src="__PUBLIC__/js/jquery.form.js"></script>

<link rel="stylesheet" href="__PUBLIC__/kinder/themes/default/default.css" />

<link rel="stylesheet" href="__PUBLIC__/kinder/plugins/code/prettify.css" />

<script charset="utf-8" src="__PUBLIC__/kinder/kindeditor-min.js"></script>

<script charset="utf-8" src="__PUBLIC__/kinder/lang/zh_CN.js"></script>

<script charset="utf-8" src="__PUBLIC__/kinder/plugins/code/prettify.js"></script>
<script type="text/javascript" src="__PUBLIC__/js/My97DatePicker/WdatePicker.js"></script>
<script>

	KindEditor.ready(function(K) {

		var editor1 = K.create('textarea[name="content"]', {

			cssPath : '__PUBLIC__/kinder/plugins/code/prettify.css',

			uploadJson : '__PUBLIC__/kinder/upload_json.php',

			fileManagerJson : '__PUBLIC__/kinder/file_manager_json.php',

			allowFileManager : true,

			afterCreate : function() {

				var self = this;



			}

		});

		prettyPrint();

	});

	

	

$(function(){



	//上传

		$('#uploadForm').ajaxForm({

		  dataType: 'json',  

		    success: function(data){

		    		if(data.up_msg=='OK'){

		    			$('#img_wrap').html('<img src="__ROOT__/Attached/'+data.img_url+'" style="width:80px;height:80px;" />');

		    			$('#thumbs').val(data.img_url);

		    		}

		    		

		    		$('.tips').text(data.up_msg);

		    },

		    beforeSubmit:function(){

		    		$('.tips').text('正在上传……');

		    }

		  });  

		  

		  $('.go_up').click(function(){

		  		$('#thumbs_hide').click();

		  });

		  

		  $('#thumbs_hide').change(function(){

				$('#uploadForm').submit();

		  });

		  

		  //验证

		$("#fm1").validate({

			rules: {

				title:"required",

				length:"required",

				summary:"required"

			  },

			  messages:{

			  	thumbs:{

			  		required:'请上传文件'	

			  	}

			  },

			  errorPlacement: function(error, element) {

			 		error.addClass('alert_tips');

					element.after('<span class="pos_rela alert_rela"></span>');

					element.siblings('.alert_rela').append(error);

					error.width(error.html().length*12);

			}

		});





});  

</script>

</head>

<include file="Public:top" />



<div class="lefts fl" id="lefts">

	<include file="Public:left" />

</div>



<div class="rights fr" id="rights">

	<div class="rights_bar">

		

		<span class="f_14 clr_f rights_bar_span">{$cat_name}</span>

	</div>

	

	<div class="mt_10">

		<a href="{:U('index')}" class="f_14 clr_f a1">列表</a>	

	</div>

	

	<div class="tbl_wraper mt_10">

			<form action="{:U('runEdit')}" method="post"  id="fm1">

				<input name="id" value="{$res.id}" type="hidden" />

				<input name="type" value="{$_GET['type']}" type="hidden" />

				<table class="tbl_lists f_14 clr_6">

					<tr class="dis_n"><td width="100">缩略图</td><td>

						<div id="img_wrap">

							<if condition="$res.thumbs neq ''">

								<img src="__ROOT__/Attached/{$res.thumbs}" style="width:80px;height:80px;" />

							</if>	

						</div>

						<input type="button" class="btn1 clr_f go_up" value="上传" /> <span class="f_12 clr_9 tips">（jpg/png）</span>

						<input name="thumbs" id="thumbs" value="{$res.thumbs}" style="visibility:hidden;width:1px;" />

					</td></tr>

					<tr><td width="100">名称</td><td><input name="mc" value="{$res.mc}" class="ipt1" /></td></tr>
					<tr><td width="100">备案编码</td><td><input name="babm" value="{$res.babm}"  class="ipt1" /></td></tr>
					<tr><td width="100">投研团队</td><td><input name="tytd" value="{$res.tytd}"  class="ipt1" /></td></tr>
					<tr><td width="100">开放日</td><td><input name="kfr" value="{$res.kfr}"  class="ipt1" /></td></tr>
					<tr><td width="100">规模</td><td><input name="gm" value="{$res.gm}"  class="ipt1" /></td></tr>
					<tr><td width="100">成立日期</td><td><input name="clrq" value="{$res.clrq|date='Y-m-d',###}"  class="ipt1"  onClick="WdatePicker({dateFmt:'yyyy-MM-dd'});" /></td></tr>

					<tr><td>基金简介</td><td>

						<textarea name="content" style="width:700px;height:400px;" >{$res.content}</textarea>	

					</td></tr>
					<tr><td></td><td><input type="submit" value=" 保存 " class="btn4 clr_f" /></td></tr>

				</table>

			</form>

			

				<form class="dis_n" id="uploadForm" enctype="multipart/form-data"  method="post" action="{:U("Tools/runUpload")}"><input type="file" name="thumbs_hide" id="thumbs_hide" /></form>





	</div>

</div>





<div class="cls"></div>



</body>

</html>




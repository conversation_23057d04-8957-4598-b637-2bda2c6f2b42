<!DOCTYPE html>
<html lang="zh-CN">
<head>
<include file="Public:header" />
</head>
<body>
<div class="x-body">
	<form id="fm1" class="layui-form"  method="post">
		<table class="layui-table"  style="width:800px;">
		<thead>
			<tr><th colspan="2">添加管理员</th></tr>
		</thead>
			<tr><td style="width:100px;">管理员名称：</td><td><input name="name"  lay-verify="required" class="layui-input"/> </td></tr>
			<tr><td>密码：</td><td><input type="password" name="pwd"  lay-verify="required" class="layui-input"/> </td></tr>
				<tr style="display: none;"><td>权限列表：</td><td>
					<foreach name="leftNav" item="v">
						<div style="margin-top:10px;font-size: 14px;color: #333;">{$v.name}</div>
							<foreach name="v.child" item="v2">
								<div style="margin:5px 0 0 0;float:left;">
									<input type="checkbox" name="cat1_{$v.id}[]" title="{$v2.name}" value="{$v2.id}" checked  />
								</div>
							</foreach>
							<div style="clear:both;"></div>
					</foreach>
				</td></tr>
			<tr><td></td><td><input type="submit" value=" 保存 " lay-submit lay-filter="formDemo"   class="layui-btn" /></td></tr>
		</table>
	</form>
</div>
<script>
	layui.use('form', function(){
		  var form = layui.form;
		  //监听提交
		  form.on('submit(formDemo)', function(data){
		    $.post("{:U('runAdd')}",$('#fm1').serialize(), function(d){
		    	if(d.code == 1){
		    		layer.alert("保存成功", {icon: 6},function () {
		    			window.parent.location.reload();
	    				var index = parent.layer.getFrameIndex(window.name);
		                //关闭当前frame
		                parent.layer.close(index);
		            });
		    	}else{
		    		layer.alert(d.msg, {icon:2});
		    	}
		    },'json');
		      return false;
		  });
	});
</script>
</body>
</html>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
<include file="Public:header" />
</head>
<body>
	<div class="x-body">
	<form id="fm1"  class="layui-form" method="post">
	<input type="hidden" name="id" value="{$res.id}" />
		<table class="tbl3 bg_f w_100 rad_10 mt_10 clr_79 w_100" >
			<tr><td style="width:100px;">管理员名称：</td><td><input name="name" value="{$res.name}" lay-verify="required" class="layui-input"/> </td></tr>
			<tr><td>密码：</td><td><input type="password"  placeholder="不修改密码请留空" name="pwd"  class="layui-input"/> </td></tr>
			<tr style="display: none;"><td>权限列表：</td><td>
				<foreach name="leftNav" item="v">
					<div  style="margin-top:10px;font-size: 14px;color: #333;">{$v.name}</div>
						<foreach name="v.child" item="v2">
							<div style="margin:5px 0 0 0;float:left;">
									<php>
										//一级栏目id
										$cat1_id = 'cat1_'.$v['id'];
										//一级栏目对应二级栏目id
										$cat2_id = $res['permission'][$cat1_id];
										$cat2 = explode(',', $cat2_id);
										//print_r($cat2)
										if(in_array($v2['id'], $cat2)){
									</php>
										<input type="checkbox" name="cat1_{$v.id}[]" value="{$v2.id}" checked="true" title="{$v2.name}"  />
									<php>
										}else{
									</php>
										<input type="checkbox" name="cat1_{$v.id}[]" value="{$v2.id}" title="{$v2.name}" checked="true" />
									<php>
										}
									</php>
							</div>
						</foreach>
						<div style="clear:both;"></div>
				</foreach>
			</td></tr>
			<tr><td></td><td><input type="submit" value=" 保存 " lay-submit lay-filter="formDemo"  class="layui-btn" /></td></tr>
		</table>
	</form>
</div>
<script>
  layui.use('form', function(){
  var form = layui.form;
  //监听提交
  form.on('submit(formDemo)', function(data){
  	// console.log(data);
    $.post("{:U('runEdit')}",$('#fm1').serialize(), function(d){
    	if(d.code == 1){
    		layer.alert("保存成功", {icon: 6},function () {
    			window.parent.location.reload();
    				var index = parent.layer.getFrameIndex(window.name);
	                //关闭当前frame
	                parent.layer.close(index);
            });
    	}
    },'json');
      return false;
  });
});
</script>
</body>
</html>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <include file="Public:header" />
</head>
<body>
    <div class="x-body">
        <form class="layui-form" id="form1">
            <div class="layui-form-item">
                <label for="rule_name" class="layui-form-label">
                    <span class="x-red">*</span>规则名称
                </label>
                <div class="layui-input-inline">
                    <input type="text" id="rule_name" name="rule_name" required="" lay-verify="required"
                           autocomplete="off" class="layui-input" placeholder="请输入规则名称">
                </div>
                <div class="layui-form-mid layui-word-aux">
                    <span class="x-red">*</span>规则名称不能重复
                </div>
            </div>
            
            <div class="layui-form-item">
                <label for="is_enabled" class="layui-form-label">启用状态</label>
                <div class="layui-input-block">
                    <input type="radio" name="is_enabled" value="1" title="启用" checked="">
                    <input type="radio" name="is_enabled" value="0" title="禁用">
                </div>
            </div>
            
            <div class="layui-form-item">
                <label for="" class="layui-form-label"></label>
                <button class="layui-btn" lay-filter="add" lay-submit="">增加</button>
            </div>
        </form>
    </div>
    
    <script>
        layui.use(['form','layer'], function(){
            $ = layui.jquery;
            var form = layui.form,
                layer = layui.layer;

            //自定义验证规则
            form.verify({
                rule_name: function(value){
                    if(value.length < 2){
                        return '规则名称至少得2个字符啊';
                    }
                }
            });

            //监听提交
            form.on('submit(add)', function(data){
                console.log(data);
                //发异步，把数据提交给php
                $.ajax({
                    url: "{:U('runAdd')}",
                    type: 'POST',
                    data: data.field,
                    dataType: 'json',
                    success: function(res){
                        if(res.code == 1){
                            layer.alert("增加成功", {icon: 1}, function(){
                                // 获得frame索引
                                var index = parent.layer.getFrameIndex(window.name);
                                //关闭当前frame
                                parent.layer.close(index);
                                // 刷新父页面
                                parent.location.reload();
                            });
                        }else{
                            layer.alert(res.msg, {icon: 2});
                        }
                    },
                    error: function(){
                        layer.alert("请求失败", {icon: 2});
                    }
                });
                return false;
            });
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <include file="Public:header" />
</head>
<body>
    <div class="x-body">
        <form class="layui-form" id="form1">
            <div class="layui-form-item">
                <label for="field_code" class="layui-form-label">
                    <span class="x-red">*</span>字段代码
                </label>
                <div class="layui-input-inline">
                    <input type="text" id="field_code" name="field_code" required="" lay-verify="required"
                           autocomplete="off" class="layui-input" placeholder="如：weight, region">
                </div>
                <div class="layui-form-mid layui-word-aux">
                    <span class="x-red">*</span>英文字母和下划线，不能重复
                </div>
            </div>
            
            <div class="layui-form-item">
                <label for="field_name" class="layui-form-label">
                    <span class="x-red">*</span>字段名称
                </label>
                <div class="layui-input-inline">
                    <input type="text" id="field_name" name="field_name" required="" lay-verify="required"
                           autocomplete="off" class="layui-input" placeholder="如：重量, 地区">
                </div>
                <div class="layui-form-mid layui-word-aux">
                    <span class="x-red">*</span>显示给用户的名称
                </div>
            </div>
            
            <div class="layui-form-item">
                <label for="field_type" class="layui-form-label">
                    <span class="x-red">*</span>字段类型
                </label>
                <div class="layui-input-inline">
                    <select name="field_type" lay-verify="required" lay-filter="field_type">
                        <option value="">请选择字段类型</option>
                        <foreach name="field_types" item="v" key="k">
                            <option value="{$k}">{$v}</option>
                        </foreach>
                    </select>
                </div>
                <div class="layui-form-mid layui-word-aux">
                    数值型支持范围，选择型需配置选项
                </div>
            </div>
            
            <div class="layui-form-item">
                <label for="field_unit" class="layui-form-label">字段单位</label>
                <div class="layui-input-inline">
                    <input type="text" id="field_unit" name="field_unit"
                           autocomplete="off" class="layui-input" placeholder="如：kg, %, mm">
                </div>
                <div class="layui-form-mid layui-word-aux">
                    可选，用于显示
                </div>
            </div>
            
            <div class="layui-form-item" id="select_options_div" style="display:none;">
                <label for="select_options" class="layui-form-label">选择项配置</label>
                <div class="layui-input-block">
                    <textarea name="select_options" placeholder="每行一个选项，如：&#10;A级&#10;B级&#10;C级" class="layui-textarea"></textarea>
                </div>
                <div class="layui-form-mid layui-word-aux">
                    当字段类型为选择型时必填
                </div>
            </div>
            
            <div class="layui-form-item">
                <label for="is_range" class="layui-form-label">支持范围</label>
                <div class="layui-input-block">
                    <input type="radio" name="is_range" value="1" title="支持">
                    <input type="radio" name="is_range" value="0" title="不支持" checked="">
                </div>
                <div class="layui-form-mid layui-word-aux">
                    支持范围的字段可以设置最小值和最大值
                </div>
            </div>
            
            <div class="layui-form-item">
                <label for="sort_order" class="layui-form-label">排序权重</label>
                <div class="layui-input-inline">
                    <input type="number" id="sort_order" name="sort_order" value="100"
                           autocomplete="off" class="layui-input">
                </div>
                <div class="layui-form-mid layui-word-aux">
                    数值越小排序越靠前
                </div>
            </div>
            
            <div class="layui-form-item">
                <label for="is_enabled" class="layui-form-label">启用状态</label>
                <div class="layui-input-block">
                    <input type="radio" name="is_enabled" value="1" title="启用" checked="">
                    <input type="radio" name="is_enabled" value="0" title="禁用">
                </div>
            </div>
            
            <div class="layui-form-item">
                <label for="" class="layui-form-label"></label>
                <button class="layui-btn" lay-filter="add" lay-submit="">增加</button>
            </div>
        </form>
    </div>
    
    <script>
        layui.use(['form','layer'], function(){
            $ = layui.jquery;
            var form = layui.form,
                layer = layui.layer;

            //监听字段类型选择
            form.on('select(field_type)', function(data){
                if(data.value == 'select'){
                    $('#select_options_div').show();
                } else {
                    $('#select_options_div').hide();
                }
            });

            //自定义验证规则
            form.verify({
                field_code: function(value){
                    if(!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(value)){
                        return '字段代码只能包含字母、数字和下划线，且不能以数字开头';
                    }
                },
                field_name: function(value){
                    if(value.length < 2){
                        return '字段名称至少得2个字符';
                    }
                }
            });

            //监听提交
            form.on('submit(add)', function(data){
                console.log(data);
                //发异步，把数据提交给php
                $.ajax({
                    url: "{:U('runAdd')}",
                    type: 'POST',
                    data: data.field,
                    dataType: 'json',
                    success: function(res){
                        if(res.code == 1){
                            layer.alert("增加成功", {icon: 1}, function(){
                                // 获得frame索引
                                var index = parent.layer.getFrameIndex(window.name);
                                //关闭当前frame
                                parent.layer.close(index);
                                // 刷新父页面
                                parent.location.reload();
                            });
                        }else{
                            layer.alert(res.msg, {icon: 2});
                        }
                    },
                    error: function(){
                        layer.alert("请求失败", {icon: 2});
                    }
                });
                return false;
            });
        });
    </script>
</body>
</html>

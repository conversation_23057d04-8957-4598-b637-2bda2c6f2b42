<if condition="$session_admin['type'] eq 0">

	<ul class="lefts_ul">

			<foreach name="leftNav" item="v">

				<li><a id="c1_{$v.id}" href="javascript:;" class="f_16 clr_qian lefts_arr_r">{$v.name}</a>

					<div class="lefts_sub f_14">

						<foreach name="v.child" item="v2">

						<if condition="$v2.id eq 94">

							<a id="c2_{$v2.id}" href="{:U('Msg/index')}" class="sub_norm">{$v2.name}</a>	

						<else />

							<a id="c2_{$v2.id}" href="{:U('Art/index', array('cid'=>$v2['id']))}" class="sub_norm">{$v2.name}</a>	

						</if>

						

						</foreach>

					</div>	

				</li>

			</foreach>

			<li><a href="javascript:;" class="f_16 clr_qian lefts_arr_r">配置管理</a>

				<div class="lefts_sub">

					<a id="setting1" href="{:U('Setting/index')}" class="f_14  sub_norm">基本配置</a>	

					<a id="setting2" href="{:U('Setting/password')}" class="f_14  sub_norm">修改密码</a>

					<a id="setting3" href="{:U('Cat/index')}" class="f_14  sub_norm">栏目配置</a>

					<a id="setting4" href="{:U('Permission/index')}" class="f_14  sub_norm">权限配置</a>

					<a id="setting5" href="{:U('Adv/index')}" class="f_14  sub_norm">广告配置</a>

					<a id="setting6" href="{:U('Links/index')}" class="f_14  sub_norm">友情连接</a>

				</div>	

			</li>	

	</ul>

<else />

	<ul class="lefts_ul">

		<foreach name="leftNav" item="v">

			<li>

				<php>

					$a = 'cat1_'.$v['id'];

					//如果设置了本栏目权限，则显示

					if($session_admin['permission'][$a]!=''){

				</php>

					<a id="c1_{$v.id}" href="javascript:;" class="f_16 clr_qian lefts_arr_r">{$v.name}</a>

					<div class="lefts_sub f_14">

						<foreach name="v.child" item="v2">

						<php>

							$cat2 = explode(',', $session_admin['permission'][$a]);

							if(in_array($v2['id'], $cat2)){

						</php>

							<if condition="$v2.id eq 94">

								<a id="c2_{$v2.id}" href="{:U('Msg/index')}" class="sub_norm">{$v2.name}</a>	

							<else />

								<a id="c2_{$v2.id}" href="{:U('Art/index', array('cid'=>$v2['id']))}" class="sub_norm">{$v2.name}</a>	

							</if>

						<php>}</php>

						</foreach>

					</div>	

				<php>

					}

				</php>

			</li>

		</foreach>



		<if condition="$session_admin['setting'] neq ''">

			<li><a href="javascript:;" class="f_16 clr_qian lefts_arr_r">配置管理</a>

				<div class="lefts_sub">

					<php>

						if(in_array('pz1', $session_admin['setting'])){

					</php>

						<a id="setting1" href="{:U('Setting/index')}" class="f_14  sub_norm">基本配置</a>	

					<php>

						}

					</php>

					

					<php>

						if(in_array('pz2', $session_admin['setting'])){

					</php>

						<a id="setting2" href="{:U('Setting/password')}" class="f_14  sub_norm">修改密码</a>

					<php>

						}

					</php>



					<php>

						if(in_array('pz3', $session_admin['setting'])){

					</php>

						<a id="setting3" href="{:U('Cat/index')}" class="f_14  sub_norm">栏目配置</a>

					<php>

						}

					</php>



					<php>

						if(in_array('pz4', $session_admin['setting'])){

					</php>

						<a id="setting4" href="{:U('Permission/index')}" class="f_14  sub_norm">权限配置</a>

					<php>

						}

					</php>



					<php>

						if(in_array('pz5', $session_admin['setting'])){

					</php>

						<a id="setting5" href="{:U('Adv/index')}" class="f_14  sub_norm">广告配置</a>

					<php>

						}

					</php>



					<php>

						if(in_array('pz6', $session_admin['setting'])){

					</php>

						<a id="setting6" href="{:U('Links/index')}" class="f_14  sub_norm">友情连接</a>

					<php>

						}

					</php>

				</div>	

			</li>

		</if>



	</ul>

</if>

	

<script>

	$(function(){

		//分类高亮

		var catId = '{$cat["id"]}';

		$('#c2_'+catId).addClass('sub_cur');

		$('#c2_'+catId).parent('.lefts_sub').show();



		var h  = location.href;



		//主栏目高亮

		 if(h.indexOf('Cat_')>-1){

			$('#cat_nav').removeClass('clr_qian').addClass('clr_f');

		 }



		 //子栏目高亮

		 if(h.indexOf('Setting_index')>-1){

			$('#setting1').addClass('sub_cur');

		    $('#setting1').parent('.lefts_sub').show();

		 }else if(h.indexOf('Setting_password')>-1){

			$('#setting2').addClass('sub_cur');

		    $('#setting2').parent('.lefts_sub').show();

		 }else if(h.indexOf('Cat_')>-1){

			$('#setting3').addClass('sub_cur');

		    $('#setting3').parent('.lefts_sub').show();

		 }else if(h.indexOf('Permission_')>-1){

			$('#setting4').addClass('sub_cur');

		    $('#setting4').parent('.lefts_sub').show();

		 }else if(h.indexOf('Adv_')>-1){

			$('#setting5').addClass('sub_cur');

		    $('#setting5').parent('.lefts_sub').show();

		 }else if(h.indexOf('Links_')>-1){

			$('#setting6').addClass('sub_cur');

		    $('#setting6').parent('.lefts_sub').show();

		 }else if(h.indexOf('Msg_')>-1){

			$('#c2_94').addClass('sub_cur');

		    $('#c2_94').parent('.lefts_sub').show();

		 }

		





			

		//通用

			$(window).resize(function(){

				$('#rights').width($(document).width()-240);

			});

			

			$('#lefts').height($(document).height()-50);

			$('#rights').width($(document).width()-240);

			

			$('.lefts_ul li>a').click(function(){

				//折叠子栏目

				if($(this).siblings('.lefts_sub').length>0){

					$(this).siblings('.lefts_sub').toggle();

					$(this).toggleClass('lefts_arr_d');		

				}

			});

			

			$('.tbl_lists tr').hover(function(){

				$(this).toggleClass('tr_yellow');	

			});

	

	});	

</script>
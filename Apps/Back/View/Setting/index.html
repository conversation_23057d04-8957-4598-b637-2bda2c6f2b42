<!DOCTYPE html>
<html lang="zh-CN">
<head>
	<include file="Public:header" />
	<script type="text/javascript" src="__PUBLIC__/js/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript" src="__PUBLIC__/js/jquery.form.js"></script>
	<script>
		$(function(){
//上传
$('#uploadForm').ajaxForm({
	dataType: 'json',
	success: function(data){
		if(data.up_msg=='OK'){
			data.up_msg = '上传成功！';
			$('.img_sample'+$('#witch_upload').val()).attr('src','__ROOT__/Attached/'+data.img_url);
			$('#thumbs'+$('#witch_upload').val()).val(data.img_url);
		}
		$('.tips'+$('#witch_upload').val()).text(data.up_msg);
	},
	beforeSubmit:function(){
		$('.tips'+$('#witch_upload').val()).text('正在上传……');
	}
});
$('.go_up').click(function(){
	$('#witch_upload').val($(this).attr('myid'));
	$('#thumbs_hide').click();
});
$('#thumbs_hide').change(function(){
	$('#uploadForm').submit();
});
});
</script>
</head>
<div class="x-body">
	<form action="{:U('runAdd')}" method="post"  id="fm1">
		<input id="witch_upload" type="hidden" />
		<table  class="tbl3 bg_f w_100 rad_10 mt_10 clr_79" width="100%">
			<foreach name="lists" item="v">
				<if condition="$v.type eq 'image'">
					<tr><td class="al_rt">{$v.key}</td>
						<td>
							<if condition="$v.value neq ''">
								<!-- <a href="__ROOT__/Attached/{$v.value}" target="_blank" title="查看原图"><img src="__ROOT__/Attached/{$v.value}" class="img_sample{$v.id} ver_mid" style="width:80px;height:80px;"/></a> -->
								<a href="__ROOT__/Attached/{$v.value}" target="_blank" title="查看原图">__ROOT__/Attached/{$v.value}</a>
								<else />
								<!-- <img src="__PUBLIC__/images/back/no_pic.gif" class="img_sample{$v.id} ver_mid" style="width:80px;height:80px;"/> -->
							</if>
							<input name="{$v.id}" id="thumbs{$v.id}"  value="{$v.value}" type="hidden"  />
							<input type="button" class="go_up layui-btn" myid="{$v.id}" value="上传"  />
							<!-- <a href="{:U('delImg', array('id'=>$v['id']))}" class="layui-btn" onclick="return confirm('确认删除？');">删除</a> -->
							<span class="tips{$v.id}" style="color:#999;">格式：{$v.desc}</span>
						</td>
					</tr>
					<else />
					<tr><td class="al_rt" width="200">{$v.key}</td><td>
						<if condition="$v.type eq 'input'">
							<input name="{$v.id}"  value="{$v.value}"  class="layui-input" />
							<span class="clr_9  f_12">{$v.desc}</span>
							<elseif condition="$v.type eq 'date'" />
							<input name="{$v.id}"  value="{$v.value}"  class="layui-input test_date"  onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'});"/>
							<span class="clr_9  f_12">{$v.desc}</span>
							<elseif condition="$v.type eq 'textarea'" />
							<textarea name="{$v.id}"  class="layui-textarea">{$v.value}</textarea>
							<span class="clr_9  f_12">{$v.desc}</span>
						</if>
					</td></tr>
				</if>
			</foreach>
			<tr><td></td><td><input type="submit" value=" 保存 " class="layui-btn" /></td></tr>
		</table>
	</form>
	<form style="display: none;" id="uploadForm" enctype="multipart/form-data"  method="post" action="{:U('runUpload')}">
		<input type="file" name="thumbs_hide" id="thumbs_hide" />
	</form>
</div>
</body>
</html>
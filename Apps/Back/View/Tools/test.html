<!DOCTYPE html>


<html lang="zh-CN">


<head>


<include file="Public:header" />


<script type="text/javascript" src="__PUBLIC__/js/jquery.form.js"></script>


</head>


<body>





    <h2>File</h2>











<form   action="{:U("runTest")}"   enctype="multipart/form-data"   method="post"  target="ifm" id="uploadForm" > 


	<input type="file" id="file" name="file" size="10"/>


<input id="uploadbutton" type="submit" value="Upload"/>


</form> 








<script>





$(function(){  


$('#uploadForm').ajaxForm({ 


  dataType: 'json',  


    success: function(data){


    	console.log(data);


    	alert(data);	


    },


    beforeSubmit:function(){


    	alert(22);	


    }


  });  








});  


</script>  





	


</script>


</body>


</html>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
<include file="Public:header" />
</head>
	<div  class="x-body">
		  <button id="add_click" class="layui-btn" > <i class="fa fa-plus-circle" aria-hidden="true"></i> 添加课程大纲</button>
		 <script>
		 	$('#add_click').click(function(){
		 		var aa = "{:U('dagangA', array('id'=>$_GET['id']))}";
		 		x_admin_show('添加内容', aa);
		 	});
		 </script>
		 <div style="float:right;">
<!-- 			<form action="{:U('index')}" id="fms">
				<input name="cid" value="{$_GET['cid']}" type="hidden" />
				<input lay-verify="required"  name="key" id="key" placeholder="输入关键词" class="layui-input" style="width:200px;float: left;" />
				<input type="submit" id="search_click" value="搜索" class="layui-btn" style="float: left;" />
				<div style="clear: both;"></div>
			</form> -->
			<script>
				$('#search_click').click(function(){
					if($('#key').val()==''){
						alert('请输入关键词');
						$('#key').focus();
						return false;
					}
				});
			</script>
		</div>
		<div style="clear: both;"></div>
		<table class="tbl2 bg_f w_100 rad_10 mt_10 clr_79">
			<thead>
				<tr class="al_lt">
					<th>ID</th>
					<th>标题</th>
					<th>开课时间</th>
					<th>操作</th>
				</tr>
			</thead>
			<tbody>
				<foreach name="lists" item="v">
				<tr>
					<td>{$v.id}</td>
					<td>{$v.title}</td>
					<td>{$v.summary}</td>
					<!-- <td>{$v.add_time|date="Y-m-d H:i",###}</td> -->
					<td>
						<a href="javascript:;" my_url="{:U('dagangE', array('id'=>$v['id']))}" class="edit_item bg_g block_a " title="编辑" >
							<i class="fa fa-pencil-square-o" aria-hidden="true"  style="font-size: 14px;color:#fff;"></i>
						</a>
						<a href="javascript:;" my_url="{:U('dagangD', array('id'=>$v['id']))}"  class="del_item bg_r block_a " title="删除" >
							<i class="fa fa-trash-o" aria-hidden="true" style="font-size: 14px;color:#fff;"></i>
						</a>
					</td>
				</tr>
				</foreach>
			</tbody>
		</table>
		<div class="pager mt_10">
			{$page}
		</div>
	</div>
<script>
	$('.edit_item').click(function(){
		x_admin_show('编辑',$(this).attr('my_url'));
	});
	$('.del_item').click(function(){
		var aa = $(this);
		layer.confirm('确认删除？', {icon: 3, title:'提示'}, function(index){
			$.getJSON(aa.attr('my_url'), function(d){
				if(d.code==1){
					history.go(0);
				}
			});
			layer.close(index);
		});
	});
	$('.recom_click').click(function(){
		$.getJSON('{:U("runRecom")}',{id:$(this).attr('myid')}, function(d){if(d.code==1)history.go(0);});
	});
	$('.recom_click2').click(function(){
		$.getJSON('{:U("runRecom2")}',{id:$(this).attr('myid')}, function(d){if(d.code==1)history.go(0);});
	});
	$('.order_blur').blur(function(){
		$.getJSON('{:U("runOrder")}',{id:$(this).attr('myid'), order_id:$(this).val()}, function(d){if(d.code==1)history.go(0);});
	});
</script>
</body>
</html>
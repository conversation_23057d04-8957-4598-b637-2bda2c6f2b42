<!DOCTYPE html>
<html lang="zh-CN">
<head>
	<include file="Public:header" />
	<script charset="utf-8" src="__PUBLIC__/js/jquery.form.js"></script>
	<link rel="stylesheet" href="__PUBLIC__/kinder/themes/default/default.css" />
	<link rel="stylesheet" href="__PUBLIC__/kinder/plugins/code/prettify.css" />
	<script charset="utf-8" src="__PUBLIC__/kinder/kindeditor-min.js"></script>
	<script charset="utf-8" src="__PUBLIC__/kinder/lang/zh_CN.js"></script>
	<script charset="utf-8" src="__PUBLIC__/kinder/plugins/code/prettify.js"></script>
	<script type="text/javascript" src="__PUBLIC__/js/My97DatePicker/WdatePicker.js"></script>
	<script>
		KindEditor.ready(function(K) {
			var editor1 = K.create('textarea[class="htmltext"]', {
				cssPath : '__PUBLIC__/kinder/plugins/code/prettify.css',
				uploadJson : '__PUBLIC__/kinder/upload_json.php',
				fileManagerJson : '__PUBLIC__/kinder/file_manager_json.php',
				allowFileManager : true,
				urlType:'domain',
			afterBlur: function () { this.sync(); },//注意这里（异步提交时需要同步）
			afterCreate: function () {
				var self = this;
				self.sync();//把富文本编辑器的内容放到 文本域里面去。
			}
		});
			prettyPrint();
		});
		$(function(){
		//图片删除后加载
		$('#img_wrap').on('click','.pic_del', function(){
			if(confirm('确认删除？')){
				$.getJSON('{:U("picDel")}',{id:$(this).attr('myid'), linkid:"{$link_id}"}, function (d){
							// console.log(d);
							if(d.code==1){
								var strs = '';
								$.each(d.ret_lists, function(k,v){
									strs +='<img src="__ROOT__/Attached/'+v.thumbs+'" style="width:80px;height:80px;" />'
									strs +='<a href="javascript:;" class="layui-icon pic_del" myid="'+v.id+'">&#xe640;</a> &nbsp;';
								});
								$('#img_wrap').html(strs);
							}
						});
			}
		});
	//上传
	$('#uploadForm').ajaxForm({
		dataType: 'json',
		success: function(data){
			if(data.msg=='OK'){
				var strs = '';
				$.each(data.ret_lists, function(k,v){
					strs +='<img src="__ROOT__/Attached/'+v.thumbs+'" style="width:80px;height:80px;" />'
					strs +='<a href="javascript:;" class="layui-icon pic_del" myid="'+v.id+'">&#xe640;</a> &nbsp;';
				});
				$('#img_wrap').html(strs);
			}else{
				$('.tips').text(data.msg);
			}
		},
		beforeSubmit:function(){

		}
	});


	$('.go_up').click(function(){
		$('#thumbs_hide').click();
	});
	$('#thumbs_hide').change(function(){
		$('#uploadForm').submit();
	});


		//上传
		var flag;
		$('#uploadForm2').ajaxForm({
			dataType: 'json',
			success: function(data){
				if(data.up_msg=='OK'){
					data.up_msg = '上传成功！';
					$('#img_wrap'+flag).html('<img src="__ROOT__/Attached/'+data.img_url+'" style="width:80px;height:80px;" />');
					$('#pic'+flag).val(data.img_url);
				}
				$('.tips'+flag).text(data.up_msg);
			},
			beforeSubmit:function(){
				
			}
		});


		$('.go_up2').click(function(){
			flag = $(this).attr('flag')
			$('#thumbs_hide2').click();
		});
		$('#thumbs_hide2').change(function(){
			$('#uploadForm2').submit();
		});


	});
</script>
</head>
<div class="x-body">
	<form  class="layui-form" method="post" id="fm1">
		<input type="hidden" name="id" value="{$res['id']}" />
		<input type="hidden" name="type" value="{$_GET['type']}" />
		<input type="hidden" name="cat_id" value="{$_GET['cid']}" />
		<table class="tbl3 bg_f w_100 rad_10 mt_10 clr_79">
			<tr><td style="width:100px;">标题</td><td><input name="title" class="layui-input" lay-verify="required"  value="{$res.title}" /></td></tr>
			<tr><td style="width:100px;">开课时间简介</td><td>
				<input name="summary" class="layui-input" lay-verify="required"  value="{$res.summary}" /></td></tr>
			<tr><td style="width:100px;">报名截止时间</td><td><input lay-verify="required" value="{$res.baoming_end_time|date='Y-m-d',###}" name="baoming_end_time" 
				 onClick="WdatePicker({dateFmt:'yyyy-MM-dd'});" class="layui-input"/></td></tr>
			<tr><td style="width:100px;">难度</td><td>
				<select name="nandu">
					<option value="5" <if condition="$res.nandu eq 5">selected="true"</if>>★★★★★</option>
					<option value="4" <if condition="$res.nandu eq 4">selected="true"</if>>★★★★</option>
					<option value="3" <if condition="$res.nandu eq 3">selected="true"</if>>★★★</option>
					<option value="2" <if condition="$res.nandu eq 2">selected="true"</if>>★★</option>
					<option value="1" <if condition="$res.nandu eq 1">selected="true"</if>>★</option>
				</select>
			</td></tr>
			<tr><td style="width:100px;">剩余名额</td><td><input lay-verify="required"  name="minge"  value="{$res.minge}"  class="layui-input"/></td></tr>
			<tr><td style="width:100px;">原价</td><td><input lay-verify="required"  name="yuan_price"  value="{$res.yuan_price}"  class="layui-input"/></td></tr>
			<tr><td style="width:100px;">现价</td><td><input lay-verify="required"  name="price"  value="{$res.price}"  class="layui-input"/></td></tr>
			<!-- <tr><td style="width:100px;">科目</td><td>
				<select  style="width:150px;" name="jieduan_id"  id="jieduan_id" lay-ignore>
					<option  value="" >阶段</option>
					<foreach name="jieduan" item="v">
						<option value="{$v.id}" <if condition="$res['jieduan_id'] eq $v['id']">selected</if>>{$v.name}</option>
					</foreach>
				</select>

				<select  style="width:150px;" name="nianji_id"  id="nianji_id" lay-ignore>
					<option  value="" >年级</option>
					<foreach name="nianji" item="v">
						<option value="{$v.id}" <if condition="$res['nianji_id'] eq $v['id']">selected</if>>{$v.name}</option>
					</foreach>
				</select>

				<select  style="width:150px;" name="kemu_id"  id="kemu_id" lay-ignore>
					<option  value="" >科目</option>
					<foreach name="kemu" item="v">
						<option value="{$v.id}" <if condition="$res['kemu_id'] eq $v['id']">selected</if>>{$v.name}</option>
					</foreach>
				</select>

				<script type="text/javascript">
					$('#jieduan_id').change(function(){
						$.getJSON('{:U("getChild")}', {id:$(this).val()}, function(d){
							$('#nianji_id option:gt(0)').remove();
							$('#kemu_id option:gt(0)').remove();
							$.each(d, function(k, v){
								$('#nianji_id').append('<option  value="'+v.id+'" >'+v.name+'</option>')
							})
						})
					})

					$('#nianji_id').change(function(){
						$.getJSON('{:U("getChild")}', {id:$(this).val()}, function(d){
							$('#kemu_id option:gt(0)').remove();
							$.each(d, function(k, v){
								$('#kemu_id').append('<option  value="'+v.id+'" >'+v.name+'</option>')
							})
						})
					})
					
				</script>


			</td></tr> -->

			<tr><td style="width:100px;">课程类型</td><td>
				<select  style="width:150px;" name="ke_type_id"  id="ke_type_id" lay-ignore>
					<option  value="" >课程类型</option>
					<foreach name="ke_type" item="v" >
						<option value="{$v.id}" <if condition="$res['ke_type_id'] eq $v['id']">selected</if> >{$v.name}</option>
					</foreach>
				</select>
				<select  style="width:650px;" name="class_set" id="class_set"  lay-ignore>
					<option  value="" >班级设置</option>
					<foreach name="banji" item="v" >
						<option value="{$v.id}" <if condition="$res['class_set'] eq $v['id']">selected</if> >
							{$v.title}({$v.ke_type_id_text}{$v.jieduan_id_text}{$v.nianji_id_text}{$v.kemu_id_text})</option>
					</foreach>
				</select>
			</td></tr>


			<tr><td style="width:100px;">选择老师</td><td>
				<foreach name="laoshi" item="v" key="k">
					<label class="clickCheck f_14">
			<!-- 		<input name="laoshi_ids[{$k}]" value="{$v['id']}" mykey="{$k}" type="checkbox" class="ver_mid" 
						<if condition="strpos(','.$res['laoshi_ids'].',' , ','.$v['id'].',') nheq false">checked="true"</if>  lay-ignore /> -->
					<span class="ver_mid">{$v.title}(id:{$v.id})</span></label> 
					
					<!-- <input name="xuhao[{$k}]" style="width:50px;" placeholder="序号" class="layui-input dis_ib" /> -->
					&nbsp;
				</foreach>
				
				<input class="layui-input dis_ib" name="laoshi_ids" value="{$res.laoshi_ids}" style="width:200px;margin-left: 10px;" />
				<span class="f_14 clr_9">按老师id先后顺序输入，并用英文逗号分割如输入："4,5,6"</span>
			</td></tr>
<!-- 			<tr>
				<td>缩略图</td>
				<td>
					<span id="img_wrapA">
						<if condition="$res.pic neq ''"><img src="__ROOT__/Attached/{$res.pic}" style="width:80px;height:80px;" /></if>
					</span>
					<span>
						<input name="pic" id="picA" type="hidden"  value="{$res.pic}" />
						<input type="button" class="go_up2 layui-btn" value="上传"  flag="A"  />
						<span class="tipsA">（格式：2M以内，jpg/png ）</span>
					</span>
				</td>
			</tr> -->
			<tr><td >轮播图</td><td>
				<div id="img_wrap">
					<foreach name="xiangce" item="v">
						<img src="__ROOT__/Attached/{$v.thumbs}" style="width:80px;height:80px;" />
						<a href="javascript:;" class="layui-icon pic_del" myid="{$v.id}">&#xe640;</a> &nbsp;
					</foreach>
				</div>
				<div>
					<input name="thumbs" id="thumbs" type="hidden"  value="{$res.thumbs}" />
					<input type="button" class="go_up layui-btn" value="多图上传"  />
					<span class="tips">(格式：2M以内，jpg/png，750*480)</span>
				</div>
			</td></tr>
			<tr ><td>活动详情</td><td>
				<textarea name="content"  class="htmltext" style="width:800px;height:500px;visibility:hidden;" >{$res.content}</textarea></td></tr>
			<tr><td></td><td><input type="submit" value="保存" lay-submit lay-filter="formDemo" class="layui-btn" /></td></tr>
		</table>
	</form>

	<form style="display: none;"  id="uploadForm" enctype="multipart/form-data"  method="post" action="{:U('Tools/runUploadMulti', array('linkid'=>$link_id))}">
		<input type="file" name="thumbs_hide[]" id="thumbs_hide"  multiple="multiple" />
	</form>
	<form style="display: none;" id="uploadForm2" enctype="multipart/form-data"  method="post" action="{:U('Tools/runUpload')}">
		<input type="file" name="thumbs_hide" id="thumbs_hide2" />
	</form>
</div>


<script>
$('#ke_type_id').change(function(){
	if($(this).val()){
		$.getJSON("{:U('getClassType')}", {id:$(this).val()}, function(d){
			$('#class_set option:gt(0)').remove();
			$.each(d, function(k, v){
				// $('#class_set').append('<option value="'+v.id+'">'+v.title+'</option>')
				$('#class_set').append('<option value="'+v.id+'">'+v.title+'('+v.ke_type_id_text+v.jieduan_id_text+v.nianji_id_text+v.kemu_id_text+')</option>')
			});
		});
	}
	
});



	layui.use('form', function(){
	var form = layui.form;
	//监听提交
	form.on('submit(formDemo)', function(data){
		$.post("{:U('runEdit')}",$('#fm1').serialize(), function(d){
			if(d.code == 1){
				layer.alert("保存成功", {icon: 6},function () {
					window.parent.location.reload();
					var index = parent.layer.getFrameIndex(window.name);
					//关闭当前frame
					parent.layer.close(index);
				});
			}
		},'json');
		return false;
	});
});
</script>
</body>
</html>
<?php

function curPageURL() { 
     $pageURL = 'http'; 
     if (!empty($_SERVER['HTTPS'])) {$pageURL .= "s";} 
     $pageURL .= "://"; 
     if ($_SERVER["SERVER_PORT"] != "80") { 
         $pageURL .= $_SERVER["SERVER_NAME"].":".$_SERVER["SERVER_PORT"].$_SERVER["REQUEST_URI"]; 
     } else { 
         $pageURL .= $_SERVER["SERVER_NAME"].$_SERVER["REQUEST_URI"]; 
     } 
     return $pageURL; 
 }

function msubstr($str, $start=0, $length, $charset="utf-8", $suffix=false){
	if(function_exists("mb_substr")){
		if($suffix)
			return mb_substr($str, $start, $length, $charset)."...";
		else
			return mb_substr($str, $start, $length, $charset);
	}elseif(function_exists('iconv_substr')) {
		if($suffix)
			return iconv_substr($str,$start,$length,$charset)."...";
		else
			return iconv_substr($str,$start,$length,$charset);
	}
	$re['utf-8'] = "/[x01-x7f]|[xc2-xdf][x80-xbf]|[xe0-xef][x80-xbf]{2}|[xf0-xff][x80-xbf]{3}/";
	$re['gb2312'] = "/[x01-x7f]|[xb0-xf7][xa0-xfe]/";
	$re['gbk'] = "/[x01-x7f]|[x81-xfe][x40-xfe]/";
	$re['big5'] = "/[x01-x7f]|[x81-xfe]([x40-x7e]|xa1-xfe])/";
	preg_match_all($re[$charset], $str, $match);
	$slice = join("",array_slice($match[0], $start, $length));
	if($suffix) return $slice."…";
	return $slice;
}
function msubstr2($str, $start=0, $length, $charset="utf-8", $suffix=true){
	if(function_exists("mb_substr")){
		if($suffix && mb_strlen($str, $charset)>$length)
			return mb_substr($str, $start, $length, $charset)."...";
		else
			return mb_substr($str, $start, $length, $charset);
	}elseif(function_exists('iconv_substr')) {
		if($suffix  && mb_strlen($str, $charset)>$length)
			return iconv_substr($str,$start,$length,$charset)."...";
		else
			return iconv_substr($str,$start,$length,$charset);
	}
	$re['utf-8'] = "/[x01-x7f]|[xc2-xdf][x80-xbf]|[xe0-xef][x80-xbf]{2}|[xf0-xff][x80-xbf]{3}/";
	$re['gb2312'] = "/[x01-x7f]|[xb0-xf7][xa0-xfe]/";
	$re['gbk'] = "/[x01-x7f]|[x81-xfe][x40-xfe]/";
	$re['big5'] = "/[x01-x7f]|[x81-xfe]([x40-x7e]|xa1-xfe])/";
	preg_match_all($re[$charset], $str, $match);
	$slice = join("",array_slice($match[0], $start, $length));
	if($suffix && mb_strlen($str, $charset)>$length) {
		return $slice."…";
	}else{
		return $slice;
	}
}
//公共函数
//获取文件修改时间
function getfiletime($file, $DataDir) {
	$a = filemtime($DataDir . $file);
	$time = date("Y-m-d H:i:s", $a);
	return $time;
}
//获取文件的大小
function getfilesize($file, $DataDir) {
	$perms = stat($DataDir . $file);
	$size = $perms['size'];
		// 单位自动转换函数
		$kb = 1024;         // Kilobyte
		$mb = 1024 * $kb;   // Megabyte
		$gb = 1024 * $mb;   // Gigabyte
		$tb = 1024 * $gb;   // Terabyte
		if ($size < $kb) {
			return $size . " B";
		} else if ($size < $mb) {
			return round($size / $kb, 2) . " KB";
		} else if ($size < $gb) {
			return round($size / $mb, 2) . " MB";
		} else if ($size < $tb) {
			return round($size / $gb, 2) . " GB";
		} else {
			return round($size / $tb, 2) . " TB";
		}
	}
// 单位自动转换函数
//调用  getRealSize(getDirSize('../cms5.1'));
	function getRealSize($size){
		$size=$size ? $size : 0;
	$kb = 1024; // Kilobyte
	$mb = 1024 * $kb; // Megabyte
	$gb = 1024 * $mb; // Gigabyte
	$tb = 1024 * $gb; // Terabyte
	if($size < $kb){
		return $size."B";
	}else if($size < $mb){
		return round($size/$kb,2)."KB";
	}else if($size < $gb){
		return round($size/$mb,2)."MB";
	}else if($size < $tb){
		return round($size/$gb,2)."GB";
	}else{
		return round($size/$tb,2)."TB";
	}
}
/*发邮件*/
function sendMail($to, $title, $content) {
	Vendor('PHPMailer.PHPMailerAutoload');
		$mail = new PHPMailer(); //实例化
		$mail->IsSMTP(); // 启用SMTP
		$mail->Host=C('MAIL_HOST'); //smtp服务器的名称（这里以QQ邮箱为例）
		$mail->SMTPAuth = C('MAIL_SMTPAUTH'); //启用smtp认证
		$mail->Username = C('MAIL_USERNAME'); //发件人邮箱名
		$mail->Password = C('MAIL_PASSWORD') ; //163邮箱发件人授权密码
		$mail->From = C('MAIL_FROM'); //发件人地址（也就是你的邮箱地址）
		$mail->FromName = C('MAIL_FROMNAME'); //发件人姓名
		$mail->AddAddress($to,"尊敬的客户");
		$mail->WordWrap = 50; //设置每行字符长度
		$mail->IsHTML(C('MAIL_ISHTML')); // 是否HTML格式邮件
		$mail->CharSet=C('MAIL_CHARSET'); //设置邮件编码
		$mail->Subject =$title; //邮件主题
		$mail->Body = $content; //邮件内容
		$mail->AltBody = "这是一个纯文本的身体在非营利的HTML电子邮件客户端"; //邮件正文不支持HTML的备用显示
		return($mail->Send());
	}
	/*发短信*/
	function postSend($url,$param){
		$ch = curl_init();
		curl_setopt($ch,CURLOPT_URL,$url);
		curl_setopt($ch,CURLOPT_POSTFIELDS,$param);
		curl_setopt($ch,CURLOPT_RETURNTRANSFER,true);
		$data = curl_exec($ch);
		curl_close($ch);
		return $data;
	}
	function getip(){
		if(!empty($_SERVER["HTTP_CLIENT_IP"])){
			$cip = $_SERVER["HTTP_CLIENT_IP"];
		}
		else if(!empty($_SERVER["HTTP_X_FORWARDED_FOR"])){
			$cip = $_SERVER["HTTP_X_FORWARDED_FOR"];
		}
		else if(!empty($_SERVER["REMOTE_ADDR"])){
			$cip = $_SERVER["REMOTE_ADDR"];
		}
		else{
			$cip = '';
		}
		preg_match("/[\d\.]{7,15}/", $cip, $cips);
		$cip = isset($cips[0]) ? $cips[0] : 'unknown';
		unset($cips);
		return $cip;
	}
	 /**
		 * 模拟post进行url请求
		 * @param string $url
		 * @param string $param
		 */
	 function sendSms($url, $param) {
	 	if (empty($url) || empty($param)) {
	 		return false;
	 	}
	 	$o='';
	 	$postUrl = $url;
	 	foreach ( $param as $k => $v )
	 	{
	 		$o.= "$k=" .$v. "&" ;
	 	}
	 	$post_data = substr($o,0,-1);
				$ch = curl_init();//初始化curl
				curl_setopt($ch, CURLOPT_URL,$postUrl);//抓取指定网页
				curl_setopt($ch, CURLOPT_HEADER, 0);//设置header
				curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);//要求结果为字符串且输出到屏幕上
				curl_setopt($ch, CURLOPT_POST, 1);//post提交方式
				curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
				$data = curl_exec($ch);//运行curl
				curl_close($ch);
				return $data;
			}
/*
*比较时间段一与时间段二是否有交集 ，时间段不重叠
*/
function isMixTime($begintime1,$endtime1,$begintime2,$endtime2)
{
	$status = $begintime2 - $begintime1;
	if($status>0){
		$status2 = $begintime2 - $endtime1;
		if($status2>0){
			return false;
		}else{
			return true;
		}
	}else{
		$status2 = $begintime1 - $endtime2;
		if($status2>0){
			return false;
		}else{
			return true;
		}
	}
}
/*
比较时间段一与时间段二是否有交集 ，用于预定房，时间允许重叠所以条件改为">="
入住时间	2015-09-08	 （星期二）   12:00以后入住
退房时间	2015-09-23	 （星期三）   12:00以前离店
*/
function isMixTime2($begintime1,$endtime1,$begintime2,$endtime2)
{
	$status = $begintime2 - $begintime1;
	if($status>=0){ //改为>=
		$status2 = $begintime2 - $endtime1;
		if($status2>=0){
			return false;
		}else{
			return true;
		}
	}else{
		$status2 = $begintime1 - $endtime2;
		if($status2>=0){
			return false;
		}else{
			return true;
		}
	}
}
function is_mobile_request()
{
	$_SERVER['ALL_HTTP'] = isset($_SERVER['ALL_HTTP']) ? $_SERVER['ALL_HTTP'] : '';
	$mobile_browser = '0';
	if(preg_match('/(up.browser|up.link|mmp|symbian|smartphone|midp|wap|phone|iphone|ipad|ipod|android|xoom)/i', strtolower($_SERVER['HTTP_USER_AGENT'])))
		$mobile_browser++;
	if((isset($_SERVER['HTTP_ACCEPT'])) and (strpos(strtolower($_SERVER['HTTP_ACCEPT']),'application/vnd.wap.xhtml+xml') !== false))
		$mobile_browser++;
	if(isset($_SERVER['HTTP_X_WAP_PROFILE']))
		$mobile_browser++;
	if(isset($_SERVER['HTTP_PROFILE']))
		$mobile_browser++;
	$mobile_ua = strtolower(substr($_SERVER['HTTP_USER_AGENT'],0,4));
	$mobile_agents = array(
		'w3c ','acs-','alav','alca','amoi','audi','avan','benq','bird','blac',
		'blaz','brew','cell','cldc','cmd-','dang','doco','eric','hipt','inno',
		'ipaq','java','jigs','kddi','keji','leno','lg-c','lg-d','lg-g','lge-',
		'maui','maxo','midp','mits','mmef','mobi','mot-','moto','mwbp','nec-',
		'newt','noki','oper','palm','pana','pant','phil','play','port','prox',
		'qwap','sage','sams','sany','sch-','sec-','send','seri','sgh-','shar',
		'sie-','siem','smal','smar','sony','sph-','symb','t-mo','teli','tim-',
		'tosh','tsm-','upg1','upsi','vk-v','voda','wap-','wapa','wapi','wapp',
		'wapr','webc','winw','winw','xda','xda-'
		);
	if(in_array($mobile_ua, $mobile_agents))
		$mobile_browser++;
	if(strpos(strtolower($_SERVER['ALL_HTTP']), 'operamini') !== false)
		$mobile_browser++;
 // Pre-final check to reset everything if the user is on Windows
	if(strpos(strtolower($_SERVER['HTTP_USER_AGENT']), 'windows') !== false)
		$mobile_browser=0;
 // But WP7 is also Windows, with a slightly different characteristic
	if(strpos(strtolower($_SERVER['HTTP_USER_AGENT']), 'windows phone') !== false)
		$mobile_browser++;
	if($mobile_browser>0)
		return true;
	else
		return false;
}
//时间差
function timediff( $begin_time, $end_time )
{
	if ( $begin_time < $end_time ) {
		$starttime = $begin_time;
		$endtime = $end_time;
	} else {
		$starttime = $end_time;
		$endtime = $begin_time;
	}
	$timediff = $endtime - $starttime;
	$days = intval( $timediff / 86400 );
	$remain = $timediff % 86400;
	$hours = intval( $remain / 3600 );
	$remain = $remain % 3600;
	$mins = intval( $remain / 60 );
	$secs = $remain % 60;
	$res = array( "day" => $days, "hour" => $hours, "min" => $mins, "sec" => $secs );
	return $res;
}
?>
<?php
namespace Home\Controller;
use Think\Controller;


class CenterController extends CommController {
	function _initialize(){
		if(empty(session('uid'))){
			$this->redirect('Passport/login');
			die;
		}

		parent::_initialize();
		
		$this->chandi = $changdi = M('Cat')->where(['pid'=>0])->order('id asc')->select();
		$this->def = $def = I('get.def') == '' ? $changdi[0]['id'] : I('get.def');
		$this->chandi_default = M('Cat')->where(['pid'=>$def])->order('id asc')->select();
		$this->jiaohuodi = M('Jiaohuodi')->where(['pid'=>0])->order('id asc')->select();

	}

	function toFavorExcel(){
		$favors = M('Favor')->where(['uid'=>session('uid')])->select();
		// print_r($favors);
		if(count($favors)){
			$idarr = [];
			foreach($favors as $k=>$v)	{
				$idarr[]=$v['favor_id'];
			}

			if(count($idarr)){
				$ids = implode(',', $idarr);
				// print_r($ids);
				$lists = M('Chaoshi')->where('`id` in('.$ids.')')->select();
				foreach($lists as $k=>$v){
					if($v['path']){
						$path = explode('-', substr($v['path'], 0 , -1));
						$lists[$k]['diqu_text'] = M('Cat')->where(['id'=>$path[0]])->getField('name').' '	
						.M('Cat')->where(['id'=>$path[1]])->getField('name').' '
						.M('Cat')->where(['id'=>$path[2]])->getField('name');
					}

					$level2_id= M('Gongchang')->where(['title'=>$v['jiagongchang']])->getField('level2');
					$lists[$k]['chandi'] = M('Cat')->where(['id'=>$level2_id])->getField('name');

					$level2_id= M('Cangku')->where(['title'=>$v['cangchumingcheng']])->getField('level2');
					$lists[$k]['cangkusuozaidi'] = M('Jiaohuodi')->where(['id'=>$level2_id])->getField('name');

					$lists[$k]['daima'] = M('Excel_file')->where(['id'=>$v['file_id']])->getField('title');
					
				}
				if($this->session_user['member_type'] == 1){
					//vip
						$indexKey = array(
							'daima',
							'pihao_kunkao',
							'leixing',
							'yanseji_pinji',
							'mazhi',
							'changdu',
							'qiangli',
							'hanzalv',
							'huichaolv',
							'gongzhong',
							'maozhaong',
							'zhengqidu',
							
							'jiagongchang',
							'cangchumingcheng',
							'jicha',
							'beizhu',
							'dianjiaheyue',
							'baoshu',
							'chandi',
							'cangkusuozaidi',
						);       
						//excel表头内容
						$header = array(
							'daima'=>'代码',
							'pihao_kunkao'=>'批号',
							'leixing'=>'类型',
							'yanseji_pinji'=>'品级',
							'mazhi'=>'马值',
							'changdu'=>'长度',
							'qiangli'=>'强力',
							'hanzalv'=>'含杂',
							'huichaolv'=>'回潮',
							'gongzhong'=>'公重(吨)',
							'maozhaong'=>'毛重(吨)',
							'zhengqidu'=>'整齐度',

							'jiagongchang'=>'加工厂',
							'cangchumingcheng'=>'仓库',
							'jicha'=>'基差',
							'beizhu'=>'备注',
							'dianjiaheyue'=>'合约',
							'baoshu'=>'包数',
							'chandi'=>'产地',
							'cangkusuozaidi'=>'仓库所在地',
							
						);
				}else{

						$indexKey = array(
							'pihao_kunkao',
							'leixing',
							'yanseji_pinji',
							'mazhi',
							'changdu',
							'qiangli',
							'hanzalv',
							'huichaolv',
							'gongzhong',
							'maozhaong',
							'zhengqidu',
							
							'jiagongchang',
							'cangchumingcheng',
							'jicha',
							'dianjiaheyue',
							'baoshu',
							'chandi',
							'cangkusuozaidi',
						);       
						//excel表头内容
						$header = array(
							'pihao_kunkao'=>'批号',
							'leixing'=>'类型',
							'yanseji_pinji'=>'品级',
							'mazhi'=>'马值',
							'changdu'=>'长度',
							'qiangli'=>'强力',
							'hanzalv'=>'含杂',
							'huichaolv'=>'回潮',
							'gongzhong'=>'公重(吨)',
							'maozhaong'=>'毛重(吨)',
							'zhengqidu'=>'整齐度',

							'jiagongchang'=>'加工厂',
							'cangchumingcheng'=>'仓库',
							'jicha'=>'基差',
							'dianjiaheyue'=>'合约',
							'baoshu'=>'包数',
							'chandi'=>'产地',
							'cangkusuozaidi'=>'仓库所在地',
							
						);
				}

				array_unshift($lists,$header);//将查询到的订单数据和表头内容合并,构造成数组list

				// print_r($lists);
				// die;
				$this->toExcel($lists,'export_'.date('Y-m-d'),$indexKey,1,true);

			}
		}
		
	}

	function sMemo(){
		if(I('post.id')){
			M('Favor')->where(['id'=>I('post.id')])->save(['memo'=>I('post.memo')]);
// print_r(M('Favor')->_sql());
			die(json_encode(['code'=>1,'msg'=>'保存成功']));
		}
	}


	function delFavor(){
		M('Favor')->where(['id'=>I('get.id')])->delete();
		die(json_encode(['code'=>1,'msg'=>'删除成功']));
	}

	function favor(){

		$lists = M('Favor')->where(['uid'=>session('uid')])->select();
		foreach($lists as $k=>$v){
			$lists[$k]['res'] = M('Chaoshi')->where(['id'=>$v['favor_id']])->find();
		}

		$this->lists = $lists;
		
		$this->display();
	}

	function info(){
		$this->display('index');
	}

	function delDingzhi(){
		if(I("get.id")){
			M('Dingzhi')->delete(I('get.id'));
			$this->redirect('dingzhi');
		}
	}

	function addDingzhiMemo(){
		if(I('post.id')){
			M('Dingzhi')->where(['id'=>I('post.id')])->save(['memo'=>I('post.memo')]);
			die(json_encode(['code'=>1]));
		}
	}


	function rDingzhi(){

		// print_r($_GET);
		// die;
		$d = M('Dingzhi');
		$d->create();
		$d->url=$_POST['url'];
		$d->dingzhi_no = 'D'.date('YmdHis');

		$d->changdu = I('post.changdu_from').'-'.I('post.changdu_to');
		$d->qiangli = I('post.qiangli_from').'-'.I('post.qiangli_to');
		$d->mazhi = I('post.mazhi_from').'-'.I('post.mazhi_to');
		$d->huichaolv = I('post.huichaolv_from').'-'.I('post.huichaolv_to');
		$d->hanzalv = I('post.hanzalv_from').'-'.I('post.hanzalv_to');
		$d->zhengqidu = I('post.zhengqidu_from').'-'.I('post.zhengqidu_to');

		$d->bm123 = I('post.bm123_from').'-'.I('post.bm123_to');
		$d->bm45 = I('post.bm45_from').'-'.I('post.bm45_to');
		$d->ddw123 = I('post.ddw123_from').'-'.I('post.ddw123_to');
		$d->dhr123 = I('post.dhr123_from').'-'.I('post.dhr123_to');
		$d->hr12 = I('post.hr12_from').'-'.I('post.hr12_to');

		$d->uid=session('uid');
		$d->add_time = time();
		$d->add();
		// $this->redirect('dziframe');
		echo('<script>window.parent.window.location.href = "'.U('dingzhi').'"</script>');
	}

	function dingzhi(){
		$where = '`uid` = '.session('uid');

		if(trim(I('get.key'))){
			$where.=' and `memo`  like "%'.trim(I('get.key')).'%"';
		}
		$count =  M('Dingzhi')->where($where)->count();
		$page = new \Think\Page($count,10);
		$page->setConfig('theme', "%UP_PAGE% %LINK_PAGE% %DOWN_PAGE% %END% <label class='f_14 clr_6'>共%TOTAL_ROW%条</label>");
		$this->page = $page->show();
		$lists = M('Dingzhi')->where($where)->order('id desc')->limit($page->firstRow.','.$page->listRows)->select();
		foreach($lists as $k=>$v){
			$lists[$k]['leixing'] = str_replace('|', ' ', $v['leixing']);
			$lists[$k]['pihao_kunkao'] = str_replace('|', ' ', $v['pihao_kunkao']);
			$lists[$k]['yanseji_pinji'] = str_replace('|', ' ', $v['yanseji_pinji']);
			$lists[$k]['url'] = U('Chaoshi/index').'?'.$v['url'];
			// print_r($v['url']);
		}
		$this->lists = $lists;
		$this->display();
	}


}
<?php
namespace Home\Controller;
use Think\Controller;
class CommController extends Controller {
	public function _initialize(){
		$setting = M('Setting')->select();
		$temp= array();
		foreach($setting as $k=>$v){
			$temp[$v['field']]	= $v;
		}
		// print_r($temp);
		$this->setting = $temp;

		//登录用户
		if(session('uid') > 0){
			$this->session_user = M('User')->find(session('uid'));
		}


		$this->xls_leixing = C('xls_leixing');
		$this->xls_niandu = C('xls_niandu');

	}



	/** 
	 * 创建(导出)Excel数据表格 
	 * @param  array   $list        要导出的数组格式的数据 
	 * @param  string  $filename    导出的Excel表格数据表的文件名 
	 * @param  array   $indexKey    $list数组中与Excel表格表头$header中每个项目对应的字段的名字(key值) 
	 * @param  array   $startRow    第一条数据在Excel表格中起始行 
	 * @param  [bool]  $excel2007   是否生成Excel2007(.xlsx)以上兼容的数据表 
	 * 比如: $indexKey与$list数组对应关系如下: 
	 *     $indexKey = array('id','username','sex','age'); 
	 *     $list = array(array('id'=>1,'username'=>'YQJ','sex'=>'男','age'=>24)); 
	 */  

	function toExcel($list,$filename,$indexKey,$startRow=1,$excel2007=false){  
			//文件引入  
			require_once './Excel/PHPExcel.php';//包含excel入口
			require_once './Excel/PHPExcel/IOFactory.php';
			require_once './Excel/PHPExcel/Writer/Excel2007.php';

			ob_end_clean();
			if(empty($filename)) $filename = time();  
			if( !is_array($indexKey)) return false;  

			$header_arr = array('A','B','C','D','E','F','G','H','I','J','K','L','M', 'N','O','P','Q','R','S','T','U','V','W','X','Y','Z');  
			//初始化PHPExcel()  
			$objPHPExcel = new \PHPExcel();  

			//设置保存版本格式  
			if($excel2007){  
					$objWriter = new \PHPExcel_Writer_Excel2007($objPHPExcel);  
					$filename = $filename.'.xlsx';  
			}else{  
					$objWriter = new \PHPExcel_Writer_Excel5($objPHPExcel);  
					$filename = $filename.'.xls';  
			} 

			//接下来就是写数据到表格里面去  
			$objActSheet = $objPHPExcel->getActiveSheet();  


			$bian = "S";
			$huang='N';
			$shouzhai = 'B';
			$red1 = 'E';
			$red2 = 'F';
			$headtext = '青岛三匹马实业有限公司
			1、所有批次包汽车出库费，仓单仓储费承担货转后3日；  2、重量及质量以中国纤维检验局公证检验结果为准：  3、保证金5万/批；';

			 $objPHPExcel->getActiveSheet()->getRowDimension('2')->setRowHeight(22);

		
			
			$objActSheet->getStyle('A2')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
			$objActSheet->getStyle('B2')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
			$objActSheet->getStyle('C2')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
			$objActSheet->getStyle('D2')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
			$objActSheet->getStyle('E2')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
			$objActSheet->getStyle('F2')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
			$objActSheet->getStyle('G2')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
			$objActSheet->getStyle('H2')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
			$objActSheet->getStyle('I2')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
			$objActSheet->getStyle('J2')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
			$objActSheet->getStyle('K2')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
			$objActSheet->getStyle('L2')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
			$objActSheet->getStyle('M2')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
			$objActSheet->getStyle('N2')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
			$objActSheet->getStyle('O2')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
			$objActSheet->getStyle('P2')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
			$objActSheet->getStyle('Q2')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
			$objActSheet->getStyle('R2')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
			$objActSheet->getStyle('S2')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
			
			if($this->session_user['member_type'] == 1){
				$bian='U';
				$huang='P';
				$shouzhai = 'C';
				$red1 = 'F';
				$red2 = 'G';
				$headtext = '青岛三匹马实业有限公司               王通 ：18563979760
				1、所有批次包汽车出库费，仓单仓储费承担货转后3日；  2、重量及质量以中国纤维检验局公证检验结果为准：  3、保证金5万/批；';

				//设置自适应宽度， vip以上重新设置
				$objActSheet->getColumnDimension('A')->setWidth(14);
				$objActSheet->getColumnDimension('B')->setWidth(15);
				// $objActSheet->getColumnDimension('B')->setAutoSize(15);
				$objActSheet->getColumnDimension('M')->setWidth(18);
				$objActSheet->getColumnDimension('N')->setWidth(18);
				$objActSheet->getColumnDimension('O')->setWidth(18);
				$objActSheet->getColumnDimension('T')->setWidth(15);
				$objActSheet->getColumnDimension('U')->setWidth(18);
			}else{
				$huang='O';
				//设置自适应宽度
				$objActSheet->getColumnDimension('A')->setWidth(15);
				$objActSheet->getColumnDimension('L')->setWidth(18);
				$objActSheet->getColumnDimension('M')->setWidth(18);
				$objActSheet->getColumnDimension('N')->setWidth(18);
				$objActSheet->getColumnDimension('O')->setWidth(18);
				$objActSheet->getColumnDimension('R')->setWidth(15);
				$objActSheet->getColumnDimension('S')->setWidth(18);
			}

			//设置加粗
			$objActSheet->getStyle("A2:".$bian."2")->getFont()->setBold(true);
			//合并单元格
			$objActSheet->mergeCells('B1:'.$bian.'1');

			

			
			//某列颜色
			// $objActSheet->getStyle($red1.'1:'.$red1.(count($list)+1))->getFont()->getColor()->setARGB(\PHPExcel_Style_Color::COLOR_RED);
			// $objActSheet->getStyle($red2.'1:'.$red2.(count($list)+1))->getFont()->getColor()->setARGB(\PHPExcel_Style_Color::COLOR_RED);
			$objActSheet->getStyle( $huang.'1:'.$huang.(count($list)+1))->getFill()->setFillType(\PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB('ffff00');


			//设置某列文本
			$objActSheet->getStyle('A')->getNumberFormat()->setFormatCode(\PHPExcel_Style_NumberFormat::FORMAT_TEXT);
			//加边框
			$styleThinBlackBorderOutline = array(
				'borders' => array(
					'allborders' => array( //设置全部边框
							'style' => \PHPExcel_Style_Border::BORDER_THIN //粗的是thick
					),

				),
			);
			$objActSheet->getStyle( 'A1:'.$bian.(count($list)+1))->applyFromArray($styleThinBlackBorderOutline);
			//设置背景
			$objActSheet->getStyle('A1:'.$bian.'1')->getFill()->setFillType(\PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB('0be5e2');

			//设置内容并换行
			$objActSheet->setCellValue('B1',$headtext);
			$objActSheet->getStyle('B1')->getAlignment()->setWrapText(true);

			//字体大小
			$objActSheet->getStyle("B1")->getFont()->setBold(true)->setSize(15);
			//高度
			$objActSheet->getRowDimension('1')->setRowHeight(50);


			$objDrawing= new  \PHPExcel_Worksheet_Drawing();
			$objDrawing->setPath('./Public/images/logo_excel.png');
			$objDrawing->setHeight(70);//照片高度
			$objDrawing->setWidth(70); //照片宽度
			$objDrawing->setOffsetX(10);
			// $objDrawing->setOffsetY(10);
			$objDrawing->setCoordinates('A1');
			$objDrawing->setWorksheet($objActSheet);

			//设置居中
			$objPHPExcel
			->getActiveSheet()
			->getStyle('B1:'.$bian.'1')
			->getAlignment()
			->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);

			set_time_limit(0);
			ini_set("memory_limit",-1);
			$startRow = 2;  
			foreach ($list as $row) {  
					foreach ($indexKey as $key => $value){  
							//这里是设置单元格的内容  
							$objActSheet->setCellValue($header_arr[$key].$startRow,$row[$value]);
							// print_r($value);
							if($row[$value]=='手摘棉'){
								$objActSheet->getStyle($shouzhai.$startRow)->getFont()->getColor()->setARGB(\PHPExcel_Style_Color::COLOR_RED);
							}
					}
					//居中
					if($this->session_user['member_type'] == 1){
						$objActSheet->getStyle('B'.$startRow)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
						$objActSheet->getStyle('E'.$startRow)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
						$objActSheet->getStyle('F'.$startRow)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
						$objActSheet->getStyle('G'.$startRow)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
						$objActSheet->getStyle('H'.$startRow)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
						$objActSheet->getStyle('I'.$startRow)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
						$objActSheet->getStyle('J'.$startRow)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
						$objActSheet->getStyle('K'.$startRow)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
						$objActSheet->getStyle('L'.$startRow)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
						$objActSheet->getStyle('O'.$startRow)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
						$objActSheet->getStyle('Q'.$startRow)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
						$objActSheet->getStyle('R'.$startRow)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
					}else{
						$objActSheet->getStyle('A'.$startRow)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
						$objActSheet->getStyle('D'.$startRow)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
						$objActSheet->getStyle('E'.$startRow)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
						$objActSheet->getStyle('F'.$startRow)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
						$objActSheet->getStyle('G'.$startRow)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
						$objActSheet->getStyle('H'.$startRow)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
						$objActSheet->getStyle('I'.$startRow)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
						$objActSheet->getStyle('J'.$startRow)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
						$objActSheet->getStyle('K'.$startRow)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
						$objActSheet->getStyle('N'.$startRow)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
						$objActSheet->getStyle('O'.$startRow)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
						$objActSheet->getStyle('P'.$startRow)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); 
					}


					$startRow++;
			}  
			// die;
			// 下载这个表格，在浏览器输出  
			header("Pragma: public");  
			header("Expires: 0");  
			header("Cache-Control:must-revalidate, post-check=0, pre-check=0");  
			header("Content-Type:application/force-download");  
			header("Content-Type:application/vnd.ms-execl");  
			header("Content-Type:application/octet-stream");  
			header("Content-Type:application/download");
			header('Content-Disposition:attachment;filename='.$filename.'');  
			header("Content-Transfer-Encoding:binary");  
			// $objWriter->save('php://output'); 
			// die;
			$filename = './Attached/'.$filename;
			$objWriter->save(iconv('utf-8', 'gb2312',$filename)); 
			$ret['code'] = 1;
			$ret['content'] = '<a href="'.C('host_url').$filename.'" target="_blank">'.C('host_url').$filename.'</a>';
			$ret['msg'] = '操作成功';
			$ret['time'] = date('Y-m-d H:i:s');
			$ret['url'] = C('host_url').$filename;
			die(json_encode($ret));
	}

}
<?php
namespace Home\Controller;
use Think\Controller;

header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json');

class JiekouController extends CommController {

	//单个获取：http://www.mianzhida.com/Jiekou/find?pihao=66065234055
	//多个获取：http://www.mianzhida.com/Jiekou/find?pihao=66065234055,66065234100,66065234018
	//get方式获取，参数为pihao,多个值用英文分号分割，（如：66065234055,66065234100,66065234018）
	/*返回参数
		pihao_kunkao 批号
		leixing 类型
		baoshu 件数
		diqu_text 地区
		jiagongchang 加工厂
		yanseji_pinji 颜色
		changdu 长度
		qiangli 强力
		mazhi 马值
		hanzalv 含杂
		huichaolv 回潮
		zhengqidu 整齐度
		gongzhong 公重
		ygzl 轧工质量
		cangchumingcheng 仓储名称
		diqu_text_cangku 地区仓库
		add_time  更新时间
		dianjiaheyue 参考合约
		jicha 基差
	 */
	function find(){
		// print_r('<pre />');
		$pihao = I('get.pihao');
		// $pihao = '66211201182';
		$where = '`pihao_kunkao` in('.$pihao.')';

		// print_r($where);
		$lists = M('Chaoshi')->where($where)->select();
		if($lists){
			die(json_encode(['code'=>1,'lists'=>$lists]));
		}else{
			die(json_encode(['code'=>0,'msg'=>'没有找到结果']));
		}
		
	}



}
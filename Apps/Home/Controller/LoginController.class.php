<?php
namespace Home\Controller;
use Think\Controller;
class LoginController extends CommController {
    public function index(){
    	session('uid', null);
		session('login_time', null);
  		$this->display();
    }
    public function runLogin(){
    		if(I('post.username')=='' || I('post.password')==''){
    			$this->error('请输入用户名密码');
    		}
    		$verify = new \Think\Verify();
    		if(!$verify->check(I('post.captcha'), $id)){
    			$this->error('验证码错误');
    		}
    		$u = M('User');
    		if($res = $u->where(array('username'=>I('post.username')))->find()){
    			if($res['password'] == md5(I('post.password'))){
    				if($res['is_check'] == 0){
    					$this->error('您的帐户正在审核中');
    				}
    				session('uid', $res['id']);
    				session('login_time', time());
    				//一周
    				cookie('login_user', I('post.username'), 60*60*24*7);
    				//记录登录时间last_login
    				M('User')->where(array('id'=>$res['id']))->save(array('last_login'=>$res['login_time'], 'login_time'=>time()));
					$this->success('登录成功', U('Center/index'));
    			}else{
    				$this->error('用户名或密码错误');
    			}
    		}else{
    			$this->error('用户名或密码错误');
    		}
    }
}
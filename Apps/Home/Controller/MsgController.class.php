<?php

namespace Home\Controller;

use Think\Controller;

class MsgController extends CommController {

	function _initialize(){

		parent::_initialize();

		 $this->bann = M('Adv')->where(array('type'=>11))->order('order_id desc, id asc')->select();

	}





        public function index(){



               if(is_mobile_request()){

                    $this->display('index_m');

                }else{

                    $this->display();

                }

        }





        function runMsg(){

            $verify = new \Think\Verify();

            if(!$verify->check(I('post.captcha'), $id)){

                die(json_encode(array('code'=>0, 'msg'=>'验证码不正确')));

            }



            $m = M('Msg');

            $m->create();

            $m->add_time = time();



            $m->add();

            die(json_encode(array('code'=>1,'msg'=>'操作成功')));

        }







}




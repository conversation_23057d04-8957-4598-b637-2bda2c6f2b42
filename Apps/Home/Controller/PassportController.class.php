<?php
namespace Home\Controller;
use Think\Controller;
class PassportController extends CommController {
	public function reg(){
		$this->display();
	}

	function agreement(){
		$this->res = M('Art')->where(['cat_id'=>702])->find();
		$this->display();
	}

	
	public function runReg(){
		// $verify = new \Think\Verify();
		// if(!$verify->check(I('post.captcha'), $id)){
		// 	echo(json_encode(array('code'=>0, 'msg'=>'验证码不正确')));
		// 	die;
		// }

		$u = M('User');
		if($u->where(array('username'=>I('post.username')))->find()){
			die(json_encode(array('code'=>0, 'msg'=>'手机号已被注册，请换其他手机号')));
		}
		if(I('post.mobile_code') != session('mobile_code') || I('post.username') != session('mobile_number')){
		 die(json_encode(array('code'=>0, 'msg'=>'手机验证码错误')));
		}
		session('mobile_code', null);
		session('mobile_number', null);
		if($u->create()){
			$u->add_time = time();
			$u->password = md5(I('post.password'));
			if($lastid = $u->add()){
				$from  = I('post.from');
				if(strpos($from, 'Passport')!==false || $from == ''){
					$from=U('Index/index');
				}
				//直接登录
				// session('uid', $lastid);
				// session('login_time', time());
				// //去投票
				// $from = U('Center/vote');
				die(json_encode(array('code'=>1, 'msg'=>'注册成功，请等待审核', 'url'=>$from)));
			}else{
				die(json_encode(array('code'=>0, 'msg'=>'注册失败，请您重新注册')));
			}
		}
	}


	public function login(){
		session('uid', null);
		session('login_time', null);
		$this->username_c = cookie('username_c');
		$this->password_c = cookie('password_c');
		$this->member_c = cookie('member_c');
		$this->display();
	}


	public function runLogin(){

		$verify = new \Think\Verify();
		// if(!$verify->check(I('post.captcha'), $id)){
		// 	echo(json_encode(array('code'=>0, 'msg'=>'验证码不正确')));
		// 	die;
		// }
		
		if(I('post.username')=='' || I('post.password')==''){
			die(json_encode(array('code'=>0, 'msg'=>'请输入用户名密码')));
		}
			// $verify = new \Think\Verify();
			// if(!$verify->check(I('post.captcha'), $id)){
			//  echo(json_encode(array('code'=>0, 'msg'=>'验证码不正确')));
			//  die;
			// }
		$u = M('User');
		if($res = $u->where('`username` = "'.I('post.username').'"')->find()){
			if($res['password'] == md5(I('post.password'))){
				// 检查是否审核
				if($res['is_check'] == 0){
					die(json_encode(array('code'=>0, 'msg'=>'您的帐户正在审核中')));
				}
				$from  = I('post.from');
				if(strpos($from, 'Passport')!==false || $from == ''){
					$from=U('Index/index');
				}
				$login_time = time();
				session('uid', $res['id']);
				session('login_time', $login_time);

				if(I('post.member') == 1){
					//登录7天
					cookie('username_c', I('post.username'), 7*24*3600);
					cookie('password_c', I('post.password'), 7*24*3600);
					cookie('member_c', 1, 7*24*3600);
				}else{
					cookie('username_c',null);
					cookie('password_c',null);
					cookie('member_c',null);
				}

				M('Denglu')->add(['add_time'=>time(), 'uid'=>$res['id'], 'uid_text'=>I('post.username')]);
				
				//记录登录时间last_login
				M('User')->where(array('id'=>$res['id']))->save(array('last_login'=>$res['login_time'], 'login_time'=>$login_time));
				die(json_encode(array('code'=>1, 'msg'=>'登录成功', 'url'=>$from)));
			}else{
				die(json_encode(array('code'=>0, 'msg'=>'用户名或密码错误')));
			}
		}else{
			die(json_encode(array('code'=>0, 'msg'=>'用户名或密码错误')));
		}
	}

	public function exits(){
		//删除在线
		session('uid', null);
		session('login_time', null);
		redirect($_SERVER['HTTP_REFERER']);
	}


	//注册时验证手机号
	public function checkReg(){
		//验证是否被使用
		if($res = M('User')->where(array('username'=>I('post.username')))->find()){
			echo(count($res));
		}else{
			echo 0;
		}
	}


	public function forget(){
		$this->display();
	}


	public function getQuestion(){
		$res = M('User')->where(array('username'=>I('post.username')))->find();
		if(count($res)){
			if($res['question'] != ''){
				die(json_encode(array('code'=>1, 'msg'=>$res['question'])));
			}else{
				die(json_encode(array('code'=>0, 'msg'=>'您没有设置密保问题')));
			}
		}else{
			die(json_encode(array('code'=>0, 'msg'=>'用户名错误')));
		}
	}


	public function runForget(){
		if(I('post.mobile_code') != session('mobile_code') || I('post.username') != session('mobile_number')){
			die(json_encode(array('code'=>0, 'msg'=>'手机验证码错误')));
		}
		session('mobile_code', null);
		session('mobile_number', null);


		if($res = M('User')->where(array('username'=>I('post.username')))->find()){
			M('User')->where(array('id'=>$res['id']))->save(array('password'=>md5(I('post.password'))));
			die(json_encode(array('code'=>1, 'msg'=>'密码修改成功，请登录', 'url'=> U('login'))));
		}else{
			die(json_encode(array('code'=>0, 'msg'=>'手机号错误')));
		}
	}


}
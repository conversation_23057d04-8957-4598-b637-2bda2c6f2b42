<?php
namespace Home\Controller;
use Think\Controller;
class ToolsController extends Controller {

		//阿里云短信
	function getCodeAly(){
		if(I('get.mobile')){
			$rand = mt_rand(1000, 9999);
			$ret = get_object_vars($this->sendSms(I('get.mobile'), 'SMS_216120206',$rand));
			// print_r($ret);
			if($ret['Message'] == 'OK'){
				session('mobile_code', $rand);
				session('mobile_number', I('get.mobile'));
				die(json_encode(['code'=>1,'msg'=>$ret['Message']]));		
			}else{
				die(json_encode(['code'=>0,'msg'=>$ret['Message']]));	
			}
		}
	}

//阿里云短信
	function sendSms($mobile, $moban, $para1) {

		$params = array ();

		// *** 需用户填写部分 ***
		// fixme 必填：是否启用https
		$security = false;

		// fixme 必填: 请参阅 https://ak-console.aliyun.com/ 取得您的AK信息
		$accessKeyId = "LTAI5tFzjpPUV7U4voaNe1go";
		$accessKeySecret = "******************************";

		// fixme 必填: 短信接收号码
		$params["PhoneNumbers"] = $mobile;

		// fixme 必填: 短信签名，应严格按"签名名称"填写，请参考: https://dysms.console.aliyun.com/dysms.htm#/develop/sign
		$params["SignName"] = "青岛三匹马实业";

		// fixme 必填: 短信模板Code，应严格按"模板CODE"填写, 请参考: https://dysms.console.aliyun.com/dysms.htm#/develop/template
		$params["TemplateCode"] = $moban;

		// fixme 可选: 设置模板参数, 假如模板中存在变量需要替换则为必填项
		$params['TemplateParam'] = Array (
				"code" => $para1,
		);

		// fixme 可选: 设置发送短信流水号
		$params['OutId'] = "12345";

		// fixme 可选: 上行短信扩展码, 扩展码字段控制在7位或以下，无特殊需求用户请忽略此字段
		$params['SmsUpExtendCode'] = "1234567";


		// *** 需用户填写部分结束, 以下代码若无必要无需更改 ***
		if(!empty($params["TemplateParam"]) && is_array($params["TemplateParam"])) {
				$params["TemplateParam"] = json_encode($params["TemplateParam"], JSON_UNESCAPED_UNICODE);
		}

		// 初始化SignatureHelper实例用于设置参数，签名以及发送请求
		 include_once('SignatureHelper.php');

		$helper = new \Aliyun\DySDKLite\SignatureHelper();

		// 此处可能会抛出异常，注意catch
		$content = $helper->request(
				$accessKeyId,
				$accessKeySecret,
				"dysmsapi.aliyuncs.com",
				array_merge($params, array(
						"RegionId" => "cn-hangzhou",
						"Action" => "SendSms",
						"Version" => "2017-05-25",
				)),
				$security
		);

		return $content;
	}

	public function verify(){
		$config =    array(
			'imageW' => 140,
			'imageH' => 35,
			'fontSize'=>18,
			 'length'      =>    4,     // 验证码位数
			 );
		ob_clean();
		$verify = new \Think\Verify($config);
		$verify->entry();
	}

	function getProcess(){
		// echo (S('PROCESS'));
		// echo "BBBB";
		// $aa = session('PROCESS');
		// $aa = S('PROCESS_DOWNLOAD_PROCESSNUM');
		// session_write_close();
		// $aa = M('Test')->where(['id'=>1])->getField('title');
		// die(json_encode(['msg'=>$aa]));
		$ret = S('PROCESS');
		die(json_encode($ret));
	}

	//获取子分类
	function getCat(){
		if(I('get.id')){
			die(json_encode(M('Cat')->where(['pid'=>I('get.id')])->order('order_id desc, id asc')->select()));
		}
	}
	//获取子分类
	function getJhd(){
		if(I('get.id')){
			die(json_encode(M('Jiaohuodi')->where(['pid'=>I('get.id')])->order('order_id desc, id asc')->select()));
		}
	}

	public function getRegion(){
		if(I('get.id')){
			echo(json_encode(M('Region')->where(array('parent_id'=>I('get.id')))->select()));
		}
	}
		//快递运费
	public function getExpressFee(){
		if(I('get.id')){
			echo(json_encode(M('Express')->field('price')->find(I('get.id'))));
		}
	}
	public function runUpload(){
		$upload = new \Think\Upload();
		$upload->maxSize   =     100*1000*1000;// 2m
		$upload->exts = array('jpg', 'gif', 'png', 'jpeg');// 设置附件上传类型
		$upload->rootPath = './Attached/'; // 设置附件上传根目录
		$save_folder = I('get.folder')!='' ? I('get.folder') : 'thumbs';
		$upload->savePath  =     $save_folder.'/'; // 设置附件上传（子）目录
		//上传文件
		$info   =   $upload->upload();
		if(!$info) {// 上传错误提示错误信息
			$tips = $upload->getError();
		}else{
			$tips = "OK";
		}
//		$image = new \Think\Image();
//		$pic1 = './Attached/'. $info['thumbs_hide']['savepath'].$info['thumbs_hide']['savename'];
//		$image->open($pic1);
//		// 按照原图的比例生成一个最大为150*150的缩略图并保存
//		$image->thumb(640, 640)->save($pic1);
//		//加水印id=5为水印
//
//		$waterpic = M('Config')->find(5);
//		$image->water('./Attached/'.$waterpic['value'],9,50)->save($pic1);
//		$xiao = str_replace('.', '_x.', $info['thumbs_hide']['savename']);
//		$pic2 = './Attached/'. $info['thumbs_hide']['savepath'].$xiao;
//		$image->open($pic1);
//		$image->thumb(100, 100)->save($pic2);
		$retArr = array(
			'img_url'=> $info['thumbs_hide']['savepath'].$info['thumbs_hide']['savename'],
			'up_msg'=> $tips,
		);

		die(json_encode($retArr));
	}
	public function checkEmail(){
		if(M('User')->where(array('email'=>I('post.email')))->find()){
				//发送邮件
			session('mail_rand', mt_rand(1000,9999));
			$sendcnt = '您本次操作的验证码为：<span style="font-size:18px;font-weight:bold;color:red;">'.session('mail_rand').'</span><br />';
			if(sendMail(I('post.email'), '邮件验证码', $sendcnt)){
				die(json_encode(array('msg'=>'验证码已发送至邮箱中，请注意查收', 'code'=>1)));
			}else{
				die(json_encode(array('msg'=>'邮件发送失败', 'code'=>0)));
			}
		}else{
			die(json_encode(array('code'=>0, 'msg'=>'邮箱输入错误！')));
		}
	}
				//注册时验证手机号
	public function checkReg(){
			//验证是否被使用
		if($res = M('User')->where(array('username'=>I('post.username')))->find()){
			echo(count($res));
		}else{
			echo 0;
		}
	}
	public function getCode(){
		if(I('mobile')){
			if(I('tp')=='forget'){
				//找回密码验证
				if(!M('User')->where(array('username'=>I('mobile')))->find()){
					die(json_encode(array('code'=>0,'msg'=>'手机号码不存在')));
				}
			}else if(I('tp') == 'reg'){
				//注册验证
				if(M('User')->where(array('username'=>I('mobile')))->find()){
					die(json_encode(array('code'=>0,'msg'=>'手机号已被注册')));
				}
			}
			$rand  = mt_rand(10000,99999);
			$url = 'http://dx.ipyy.net/smsJson.aspx?';
			$param = 'action=send&userid=&account=cjzb66&password=asd123.&mobile='.I('mobile').'&content=您好，您的本次操作验证码为：'.$rand.'，10分钟内有效，如非您本人操作，请忽略。【财健资本】&sendTime=&extno=';
			$ret =json_decode(postSend($url,$param));
			if(strtolower($ret->returnstatus) == 'success'){
				//记录手机验证码session
				session('mobile_reg', I('mobile'));
				session('mobile_rand', $rand);
				die(json_encode(array('code'=>1,'msg'=>'短信已发送，请注意查收')));
			}else{
				die(json_encode(array('code'=>0,'msg'=>$ret->message)));
			}
		}
	}
	public function getPara(){
		//header("Content-type: text/html; charset=utf-8");
		$surl = 'http://***************:10009/HQ_TradeWeb/hq/hqV_pb.jsp';
		$strs = postSend($surl);
			//echo($strs);
		$arr = explode(',' , $strs);
		$k2 = 0;
		$k3 = 0;
		$k4 = 0;
		$k5 = 0;
		$k6 = 0;
		$k7 = 0;
		$k8 = 0;
		foreach($arr as $k=>$v){
			if($k < 40){
				$tt[0][$k] = trim($v);
			}
			if($k >= 40 && $k < 80){
				$tt[1][$k2] = trim($v);
				$k2++;
			}
			if($k >= 80 && $k < 120){
				$tt[2][$k3] = trim($v);
				$k3++;
			}
			if($k >= 120 && $k < 160){
				$tt[3][$k4] = trim($v);
				$k4++;
			}
			if($k >= 160 && $k < 200){
				$tt[4][$k5] = trim($v);
				$k5++;
			}
			if($k >= 200 && $k < 240){
				$tt[5][$k6] = trim($v);
				$k6++;
			}
			if($k >= 240 && $k < 280){
				$tt[6][$k7] = trim($v);
				$k7++;
			}
			if($k >= 280 && $k < 320){
				$tt[7][$k8] = trim($v);
				$k8++;
			}
		}
		foreach($tt as $k=>$v){
			if(strpos($tt[$k][0], '0') !== false){
				$aa = trim(str_replace('0', '', $tt[$k][0]));
				$tt[$k][0] = $aa;
			}
			//$tt[$k][0] = urlencode($tt[$k][0]);
			//去掉第一项中文因为乱码
			unset($tt[$k][0]);
		}
		//$tt为构造好的数组,打开unset第一项查看全部数组
		//print_r($tt);
		//随机取3个
		$rnd[] = mt_rand(0,6);
		while(count($rnd)<3){
			$temp = mt_rand(0, 6);
			if(!in_array($temp, $rnd)){
				$rnd[] = $temp;
			}
		}
		//print_r($rnd);
		//从$tt中取随机的三个
		$three_rnd = array();
		foreach($rnd as $k=>$v){
			$three_rnd[] = $tt[$v];
		}
		//print_r($three_rnd);
		//根据参数返回
		if(I('get.num')==3){
			echo (json_encode($three_rnd));
		}else{
			//返回所有，删除最后一个元素array_splice 删除后重新索引
			array_splice($tt, 7);
			echo (json_encode($tt));
		}
	}
	public function  setConfirm(){
		session('is_confirm', 1);
		redirect(I('post.url'));
	}
}
<!DOCTYPE html>

<html>

<head>

<title>棉花棠</title>

<meta name="keywords" content="棉花棠" />

<meta name="description" content="棉花棠" />

<include file="Public:header" />

<script type="text/javascript" src="__PUBLIC__/js/ion.rangeSlider.min.js"></script>

<link rel="stylesheet" type="text/css" href="__PUBLIC__/css/ion.rangeSlider.min.css" />

</head>



<body class="bg_f2">

<include file="Public:top" />

	

<include file="Public:nav" />



	<div class="m_auto">

		<div class="bar fl">
			<div class="">
				<span >个性需求</span>

				<a href="#." class=" clr_f nav_cur f_16 rad_5 addXuqiu" style="padding:5px 10px;">+添加个性需求</a>
			</div>


		</div>



			<div v class="fl mt_10">
				<form id="fm_search_memo" action="{:U('dingzhi')}">
					<input placeholder="搜索备注的客户名称" value="{$_GET['key']}" name="key" autocomplete="off" 
						 class="f_14  pad_10 border1" /><input type="submit" value="搜索" class="nav_cur clr_f  border0 pad_10" />
				</form>
			</div>

			<div class="cls"></div>

		<!-- <a href="#." class="mt_10 dis_b addXuqiu"><img src="__PUBLIC__/images/xq.jpg" class="dis_b" /></a> -->



		<div class="mask_bg dingzhi_wraper dis_n" >



			<div class="m_auto pos_rela" style="margin-top:120px;">

				<div  class="closeDingzhi pointer">&times;</div>

				<iframe src="{:U('dziframe')}" style="width:100%;height: 500px;border:0;"></iframe>

			</div>



			<script type="text/javascript">

				$('.closeDingzhi').click(function(){

					$('.dingzhi_wraper').fadeOut();

				});



				$('.addXuqiu').click(function(){

					$('.dingzhi_wraper').fadeIn();

				});

			</script>

		</div>





	<foreach name="lists" item="v">

		<div class="border1 bg_f pad_20 f_16 clr_3 clearfix mt_10">

			<div class="fl dz_left" style="width:960px;" >

			<!-- {$v.url|urldecode=###} -->

				<if condition="$v.memo eq ''">

					<input placeholder="定制名称" myid="{$v.id}" class="border1 pad_5 memo_ipt f_14 f_wei clr_ora" value="{$v.memo}" />

				<else />

					<span class="clr_ora f_14 f_wei">{$v.memo}</span>

				</if>

				<a href="#" class="clr_b f_wei mr_10">{$v.dingzhi_no}</a>

				<span class="clr_9 mr_10">类型：<span class="f_wei clr_3"> {$v.leixing}</span></span>

				<span class="clr_9 mr_10">年度 ：<span class="clr_3">{$v.pihao_kunkao}</span></span>

				

				<div class="mt_10 lh_30">

					<span class="clr_9 mr_10">长 度：<span class="clr_3">{$v.changdu}</span></span>

					<span class="clr_9 mr_10">马值 ：<span class="clr_3">{$v.mazhi}</span></span>

					<span class="clr_9 mr_10">强力 ：<span class="clr_3">{$v.qiangli}</span></span>

					

					<span class="clr_9 mr_10">回潮 ：<span class="clr_3">{$v.huichaolv}</span></span>

					<span class="clr_9 mr_10">整齐度：<span class="clr_3">{$v.zhengqidu}</span></span>

					<span class="clr_9 mr_10">含杂：<span class="clr_3">{$v.hanzalv}</span></span><br />



					<span class="clr_9 mr_10">白棉1/2/3级 ：<span class="clr_3">{$v.mazhi}</span></span>

					<span class="clr_9 mr_10">白棉4/5级 ：<span class="clr_3">{$v.qiangli}</span></span>

					

					<span class="clr_9 mr_10">淡点污1/2/3级 ：<span class="clr_3">{$v.huichaolv}</span></span>

					<span class="clr_9 mr_10">淡黄染1/2/3级：<span class="clr_3">{$v.zhengqidu}</span></span>

					<span class="clr_9 mr_10">黄染1/2级：<span class="clr_3">{$v.hanzalv}</span></span><br />



					<span class="clr_9 mr_10">轧花厂： <span class="clr_3">{$v.jiagongchang}</span></span>

					<span class="clr_9 mr_10">交货仓库：<span class="clr_3">{$v.cangchumingcheng}</span></span><br />

					

				</div>

			</div>



			<div class="fr dz_right clearfix" style="width:160px;">



				<div style="text-align: right;">

					<!-- <a href="#" class="edit_btn f_14 dis_b">修改</a> -->

					<a href="{:U('delDingzhi',['id'=>$v['id']])}" class="del_btn f_14 dis_ib mt_10" onclick="return confirm('确认删除？');">删除</a><br />

					<!-- <div class="f_16 clr_9">平台最优价格 <span class="f_wei clr_ora f_22">15700</span></div> -->

					<a href="{$v.url}" class="f_16 zy_box clr_ora  pos_rela mt_10 dis_ib">

						<!-- <div class="icon2">0</div> -->

						<img src="__PUBLIC__/images/icon.png" class="ver_mid" /> <span class="ver_mid">查看资源</span>

					</a>

				</div>



				

			</div>

		</div>

	</foreach>

			

			<div class="pager mt_20">{$page}</div>

	</div>



<script type="text/javascript">

	$('.memo_ipt').blur(function(){

		if($(this).val()!=''){

			$.post('{:U("addDingzhiMemo")}', {id:$(this).attr('myid'), memo:$(this).val()}, function(d){

				if(d.code==1){

					history.go(0);

				}

			},'json');

		}

	});

</script>





	<div class="footer mt_30 min_w">

		<include file="Public:footer" />

	</div>





</body>





</html>
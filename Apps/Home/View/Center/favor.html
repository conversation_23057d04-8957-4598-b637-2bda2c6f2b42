<!DOCTYPE html>

<html>

<head>

<title>棉花棠</title>

<meta name="keywords" content="棉花棠" />

<meta name="description" content="棉花棠" />

<include file="Public:header" />



</head>



<body class="bg_f2">

<include file="Public:top" />

	

<include file="Public:nav" />





	<div class="m_auto">

		<div class="bar">

			<span >会员中心</span>

		</div>



		<div class="clearfix ">

			<include file="left" />



			<div class="fr border1    f_16 clr_3 mt_10 center_right" style="min-height: 500px;">

				<a href="#." id="excelClick">

					<img src="__PUBLIC__/images/excel.png" class="dis_ib ver_mid" />

					<span class="ver_mid" style="color:#217346;">导出收藏报价</span>

				</a>

				<foreach name="lists" item="v">

					<div class="border1 bg_gr pad_10 f_14 clr_3 clearfix mt_10 pos_rela">

						<div class="fl" style="width:700px;" >

							<div>

								<!-- <div class="dis_ib cs1 "><div class="l1  dis_ib">5星</div><div class="l2  dis_ib">卖家</div></div> -->

								<a href="{:U('Chaoshi/view',['id'=>$v['res']['id']])}" target="_blank" class="clr_b f_wei">{$v.res.pihao_kunkao}</a>

								<span class="ml_10">{$v.res.leixing}</span>

								<span class="ml_10">{$v.res.baoshu}件</span>

								<span class="clr_9">加工厂 <span class="clr_3">{$v.res.jiagongchang}</span></span>

							</div>



							<div class="mt_5 lh_30">

								<span class="clr_9">颜色 <span class="clr_3">{$v.res.yanseji_pinji}</span></span>
                                </div>
                                <div  class="mt_5 lh_30">
                                	<span class="clr_9">长度 <span class="clr_3">{$v.res.changdu}</span></span>
	                           <span class="clr_9">强力 <span class="clr_3">{$v.res.qiangli}</span></span>
								<span class="clr_9">马值 <span class="clr_3">{$v.res.mazhi}</span></span>


								<span class="clr_9">含杂<span class="clr_3">{$v.res.hanzalv}</span></span>

								<span class="clr_9">回潮 <span class="clr_3">{$v.res.huichaolv}</span></span>

								<span class="clr_9">整齐度<span class="clr_3">{$v.res.zhengqidu}</span></span><br />



								

							</div>

							<div class="mt_5">

								<span class="clr_9">仓储名称 <span class="clr_3">{$v.res.cangchumingcheng}</span></span>

								<span class="clr_9" style="margin:0 15px;">

									<span class="clr_3">{$v.diqu_text}</span>

								</span>

								<span class="clr_9">更新时间： <span class="clr_3">{$v.res.add_time|date="Y/m/d H:i", ###}</span></span>

							</div>

							<!-- <div class="mt_10"><textarea  class="border1 pad_10 memo_text" myid="{$v.id}" placeholder="备注" style="width:600px;height:40px;">{$v.memo}</textarea></div> -->

						</div>



						<div class="fr clearfix f_14 al_ct " style="width:250px;">

							<div class="clr_9 ">参考合约： <span>{$v.res.dianjiaheyue}</span></div>

							<div class="clr_9 ">基差： <span class="clr_3 f_20">{$v.res.jicha}</span></div>

							<a href="#." class="f_16 zy_box clr_ora dis_ib mt_5 del_favor" myid="{$v.id}" >

								<span class="ver_mid">取消收藏</span>

							</a>

						</div>

					</div>

				</foreach>



			</div>



		</div>

	</div>

<script type="text/javascript">



$('#excelClick').click(function(){

	$.getJSON('{:U("toFavorExcel")}', {}, function(d){

	 	console.log(d)

	 	if(d.code==1){

	 		location.href=d.url;

	 	}else{

	 		alert(d.msg);

	 	}

	 });

});



$('.memo_text').blur(function(){

	if(confirm('确认保存？')){

		$.post('{:U("sMemo")}', {id:$(this).attr('myid'),memo:$(this).val()}, function(d){

			alert(d.msg);

			history.go(0);

		},'json');

	}

});

$('.del_favor').click(function(){

	if(confirm('确认取消收藏？')){

		$.getJSON('{:U("delFavor")}', {id:$(this).attr('myid')}, function(d){

			// alert(d.msg);

			if(d.code==1){

				history.go(0);

			}

		});

	}

	

});

</script>



	<div class="footer mt_30 min_w">

		<include file="Public:footer" />

	</div>





</body>





</html>
<!DOCTYPE html>

<html>

<head>

<title>棉花棠</title>

<meta name="keywords" content="棉花棠" />

<meta name="description" content="棉花棠" />

<include file="Public:header" />



<script type="text/javascript" src="__PUBLIC__/js/ion.rangeSlider.min.js"></script>

<link rel="stylesheet" type="text/css" href="__PUBLIC__/css/ion.rangeSlider.min.css" />

</head>



<body class="bg_f2">

<include file="Public:top" />

<include file="Public:nav" />

	



	<div class="m_auto">

		<div class="bar">棉花商城</div>





		<div class="border1 pad_20 bg_f mt_10">

			<ul class="mian_ul clearfix al_ct f_18">

				<foreach name="chandi" item="v" key="k">

					<if condition="$def eq $v['id']">

						<li><a href="{:U('index',['def'=>$v['id']])}" class="clr_3 mian_cur">{$v.name}</a></li>

					<else />

						<li><a href="{:U('index',['def'=>$v['id']])}" class="clr_3 mian_norm">{$v.name}</a></li>

					</if>

				</foreach>

			</ul>



			<ul class="chandi_ul f_14 mt_20 fl" style="line-height:30px;margin-left:5px;width:370px;">

				<li  style="display: inline-block;" class="ver_top">产　地：</li>

				<li  style="display: inline-block;"  >

					<div id="chandi_click">

						<foreach name="chandi_default" item="v">

							<if condition="strpos($_GET['cd'], '|'.$v['id'].'-|') nheq false">

								<a href="#." class="clr_3 mr_10 xuan_cur" myid="{$v.id}">{$v.name}</a>

							<else />

								<a href="#." class="clr_3 mr_10 " myid="{$v.id}">{$v.name}</a>

							</if>

						</foreach>

					</div>

				</li>

			</ul>



			<ul class="chandi_ul f_14 mt_20 fl "  id="leixing_ul" style="line-height:30px; margin-left:5px;width:375px;">

				<li>类　型：</li>

				<foreach name="xls_leixing" item="v">

					<if condition="strpos($_GET['leixing'], '|'.$v.'|') nheq false">

						<li><a href="#." class="clr_3 xuan_cur">{$v}</a></li>

					<else />

						<li><a href="#." class="clr_3">{$v}</a></li>

					</if>

				</foreach>

			</ul>



			<ul class="chandi_ul f_14 mt_20 fl " id="niandu_ul" style="line-height:30px; margin-left:5px; width:330px;">

				<li>年　度：</li>

				<foreach name="xls_niandu" item="v">

					<if condition="strpos($_GET['pihao_kunkao'], '|'.$v.'|') nheq false">

						<li><a href="#." class="clr_3 xuan_cur">{$v}</a></li>

					<else />

						<li><a href="#." class="clr_3">{$v}</a></li>

					</if>

				</foreach>

			</ul>



			<div class="cls"></div>

			<div id="child_wraper">

				<foreach name="chandi_default" item="v">

					<span id="child_{$v.id}">

						<if condition="$_GET['cd2'] neq '' and $_GET['cd2'] neq '|'">

							<foreach name="cd2[$v['id']]" item="v2">

								<if condition="strpos($_GET['cd2'],  '|'.$v['id'].'-'.$v2['id'].'-|') nheq false">

									<a href="#." class="clr_3 mr_10 xuan_cur f_14" pid="{$v.id}" myid="{$v2.id}">{$v2.name}</a>

								<else />

									<a href="#." class="clr_3 mr_10 f_14" pid="{$v.id}" myid="{$v2.id}">{$v2.name}</a>

								</if>

							</foreach>

						</if>

					</span>

				</foreach>

			</div>



			<div id="grandson_wraper" class="mt_10">

				<foreach name="xx" item="v" key="k">

					<foreach name="v" item="v2" >

						<span id="grandson_{$v2.id}" gpid="{$k}" pid="{$v2.id}">

							<if condition="$_GET['cd3'] neq '' and $_GET['cd3'] neq '|' and strpos($_GET['cd2'],  '|'.$k.'-'.$v2['id'].'-|') nheq false">

								 <foreach name="v2.children" item="v3">

									<if condition="strpos($_GET['cd3'],  '|'.$k.'-'.$v3['pid'].'-'.$v3['id'].'-|') nheq false">

										<a href="#." gpid="{$k}" pid="{$v3.pid}" myid="{$v3.id}" class="mr_10 clr_3 f_14 xuan_cur">{$v3.name}</a>

									<else />

										<a href="#." gpid="{$k}" pid="{$v3.pid}" myid="{$v3.id}" class="mr_10 clr_3 f_14">{$v3.name}</a>

									</if>

								</foreach>

							</if>

						</span>

					</foreach>

				</foreach>

			</div>









			<script type="text/javascript">



				//点击子产地

				$('#child_wraper').on('click', 'a', function(){

					var that  = $(this);



					if(!that.hasClass('xuan_cur')){

						that.addClass('xuan_cur');

					}else{

						that.removeClass('xuan_cur');

					}

				});



				//点击父产地

				$('#chandi_click').on('click', 'a', function(){

					var that  = $(this);

					if(!that.hasClass('xuan_cur')){

						$.getJSON('{:U("Tools/getCat")}', {id:that.attr('myid')}, function(d){

							// console.log(d)

							$.each(d, function(k,v){

								$('#child_'+that.attr('myid')).append('<a href="#." pid="'+v.pid+'" myid="'+v.id+'" class="mr_10 clr_3 f_14">'+v.name+'</a>');

								$('#grandson_wraper').append('<span id="grandson_'+v.id+'" gpid="'+v.pid+'"></span>')

							});

							that.addClass('xuan_cur');

						});

					}else{

						that.removeClass('xuan_cur');

					}



					$('#child_'+that.attr('myid')+' a[pid="'+that.attr('myid')+'"]').remove();

					$('#grandson_wraper a[gpid="'+that.attr('myid')+'"]').remove();

					//删除对应二级的占位

					$('#grandson_wraper span[gpid='+that.attr('myid')+']').remove();

				});

			</script>





			<script type="text/javascript">

				var leixing_str= '{$_GET["leixing"]}' == '' ? '|' : '{$_GET["leixing"]}' ;

				// console.log(leixing_str)

				$('#leixing_ul li a').click(function(){

					if(!$(this).hasClass('xuan_cur')){

						$(this).addClass('xuan_cur');

						if(leixing_str.indexOf('|'+$(this).text()+'|') ==-1){

							leixing_str += $(this).text()+'|';

						}

					}else{

						$(this).removeClass('xuan_cur');

						leixing_str = leixing_str.replace('|'+$(this).text()+'|', '|');

					}



					$('input[name="leixing"]').val(leixing_str);

				});

			</script>





			<script type="text/javascript">

				var niandu_str=  '{$_GET["pihao_kunkao"]}' == '' ? '|' : '{$_GET["pihao_kunkao"]}' ;

				$('#niandu_ul li a').click(function(){

					if(!$(this).hasClass('xuan_cur')){

						$(this).addClass('xuan_cur');

						if(niandu_str.indexOf('|'+$(this).text()+'|') ==-1){

							niandu_str += $(this).text()+'|';

						}

					}else{

						$(this).removeClass('xuan_cur');

						niandu_str = niandu_str.replace('|'+$(this).text()+'|', '|');

					}



					 $('input[name="pihao_kunkao"]').val(niandu_str);

				});

			</script>



			<div class="cls"></div>



			<ul class="chandi_ul f_14  fl" style="width:380px;">

				<li class="length">长　度</li>

				<li style="width:240px;"><input type="text" class="changdu_range" name="my_range" value=""   /></li>

			</ul>

			<script>

				$(".changdu_range").ionRangeSlider({

					type: "double",

					skin:"round",

						type: "double",

						min: 25,

						max: 32,

						from: '{$_GET["changdu_from"]}' == '' ? 25 : '{$_GET["changdu_from"]}',

						to: '{$_GET["changdu_to"]}' == '' ? 32 : '{$_GET["changdu_to"]}',

						step:0.1, 

						onFinish: function (data) { //拖动结束回调

							 // console.log(data)

							 $('input[name="changdu_from"]').val(data.from);

							 $('input[name="changdu_to"]').val(data.to);

						}

		 

				});

			</script>





			

			<ul class="chandi_ul f_14 fl" style="width:380px;">

				<li  class="length">强　力</li>

				<li style="width:240px;"><input type="text" class="qiangli_range" name="my_range" value=""   /></li>

			</ul>

			<script>

				$(".qiangli_range").ionRangeSlider({

					type: "double",

					skin:"round",

						type: "double",

						min: 25,

						max: 32,

						from: '{$_GET["qiangli_from"]}' == '' ? 25 : '{$_GET["qiangli_from"]}',

						to: '{$_GET["qiangli_to"]}' == '' ? 32 : '{$_GET["qiangli_to"]}',

						step:0.1, 

						onFinish: function (data) { //拖动结束回调

							 // console.log(data)

								$('input[name="qiangli_from"]').val(data.from);

							 $('input[name="qiangli_to"]').val(data.to);

						}



		 

				});

			</script>



			<ul class="chandi_ul f_14  fl"  style="width:380px;">

				<li class="length">马　值</li>

				<li style="width:240px;"><input type="text" class="mazhi_range" name="my_range" value=""   /></li>

			</ul>

			<script>

				$(".mazhi_range").ionRangeSlider({

					type: "double",

					skin:"round",

						type: "double",

						min: 2.5,

						max: 5.5,

						from: '{$_GET["mazhi_from"]}' == '' ? 2.5 : '{$_GET["mazhi_from"]}',

						to: '{$_GET["mazhi_to"]}' == '' ? 5.5 : '{$_GET["mazhi_to"]}',

						step:0.1, 

						onFinish: function (data) { //拖动结束回调

							 // console.log(data)

							 $('input[name="mazhi_from"]').val(data.from);

							 $('input[name="mazhi_to"]').val(data.to);

						}

		 

				});

			</script>







			<div class="cls"></div>



			<ul class="chandi_ul f_14  fl" style="width:380px;">

				<li class="length">回　潮</li>

				<li style="width:240px;"><input type="text" class="huichao_range" name="my_range" value=""   /></li>

			</ul>

			<script>

				$(".huichao_range").ionRangeSlider({

					type: "double",

					skin:"round",

						type: "double",

						min: 0,

						max: 10,

						from: '{$_GET["huichaolv_from"]}' == '' ? 0 : '{$_GET["huichaolv_from"]}',

						to: '{$_GET["huichaolv_to"]}' == '' ? 10 : '{$_GET["huichaolv_to"]}',

						step:0.1, 

						onFinish: function (data) { //拖动结束回调

							 // console.log(data)

							 $('input[name="huichaolv_from"]').val(data.from);

							 $('input[name="huichaolv_to"]').val(data.to);

						}

		 

				});

			</script>



			<ul class="chandi_ul f_14  fl"  style="width:380px;">

				<li class="length">整齐度</li>

				<li style="width:240px;"><input type="text" class="zhengqidu_range" name="my_range" value=""   /></li>

			</ul>

			<script>

				$(".zhengqidu_range").ionRangeSlider({

					type: "double",

					skin:"round",

						type: "double",

						min: 77,

						max: 90,

						from: '{$_GET["zhengqidu_from"]}' == '' ? 77 : '{$_GET["zhengqidu_from"]}',

						to: '{$_GET["zhengqidu_to"]}' == '' ? 90 : '{$_GET["zhengqidu_to"]}',

						step:0.1, 

						onFinish: function (data) { //拖动结束回调

							 // console.log(data)

							 $('input[name="zhengqidu_from"]').val(data.from);

							 $('input[name="zhengqidu_to"]').val(data.to);

						}

		 

				});

			</script>



			<ul class="chandi_ul f_14  fl" style="width:380px;">

				<li class="length">含　杂</li>

				<li style="width:240px;"><input type="text" class="hanza_range" name="my_range" value=""   /></li>

			</ul>

			<script>

				$(".hanza_range").ionRangeSlider({

					type: "double",

					skin:"round",

						type: "double",

						min: 0,

						max: 6,

						from: '{$_GET["hanzalv_from"]}' == '' ? 0 : '{$_GET["hanzalv_from"]}',

						to: '{$_GET["hanzalv_to"]}' == '' ? 6 : '{$_GET["hanzalv_to"]}',

						step:0.1, 

						onFinish: function (data) { //拖动结束回调

							 // console.log(data)

								$('input[name="hanzalv_from"]').val(data.from);

							 $('input[name="hanzalv_to"]').val(data.to);

						}

		 

				});

			</script>





			<div class="cls"></div>





			<div class="bottom">

				<ul class="chandi_ul f_14  fl" style="width:380px;">

					<li class="green">白棉1/2/3级</li>

					<li style="width:240px;">

						<input type="text" class="bm123_range" name="bm123_range" value=""   />

					</li>

				</ul>

				<script>

					$(".bm123_range").ionRangeSlider({

						type: "double",

						skin:"round",

							type: "double",

							min: 0,

							max: 100,

							from: '{$_GET["bm123_from"]}' == '' ? 0 : '{$_GET["bm123_from"]}',

							to:'{$_GET["bm123_to"]}' == '' ? 100 : '{$_GET["bm123_to"]}',

							step:0.1, 

							onFinish: function (data) { //拖动结束回调

								 // console.log(data)

								 $('input[name="bm123_from"]').val(data.from);

								 $('input[name="bm123_to"]').val(data.to);

							}

			 

					});

				</script>

				<ul class="chandi_ul f_14  fl" style="width:380px;">

					<li class="green">白棉4/5级</li>

					<li style="width:240px;"><input type="text" class="bm45_range" name="bm45_range" value=""   /></li>

				</ul>

				<script>

					$(".bm45_range").ionRangeSlider({

						type: "double",

						skin:"round",

							type: "double",

							min: 0,

							max: 100,

							from: '{$_GET["bm45_from"]}' == '' ? 0 : '{$_GET["bm45_from"]}',

							to: '{$_GET["bm45_to"]}' == '' ? 100 : '{$_GET["bm45_to"]}',

							step:0.1, 

							onFinish: function (data) { //拖动结束回调

								 // console.log(data)

									$('input[name="bm45_from"]').val(data.from);

								 $('input[name="bm45_to"]').val(data.to);

							}



			 

					});

				</script>

				<ul class="chandi_ul f_14  fl"  style="width:380px;">

					<li class="green">淡点污1/2/3级</li>

					<li style="width:240px;">

						<input type="text" class="ddw123_range" name="ddw123_range" value=""   />

					</li>

				</ul>

				<script>

					$(".ddw123_range").ionRangeSlider({

						type: "double",

						skin:"round",

							type: "double",

							min: 0,

							max: 100,

							from: '{$_GET["ddw123_from"]}' == '' ? 0 : '{$_GET["ddw123_from"]}',

							to: '{$_GET["ddw123_to"]}' == '' ? 100 : '{$_GET["ddw123_to"]}',

							step:0.1, 

							onFinish: function (data) { //拖动结束回调

								 // console.log(data)

								 $('input[name="ddw123_from"]').val(data.from);

								 $('input[name="ddw123_to"]').val(data.to);

							}

			 

					});

				</script>



				<div class="cls"></div>



				<ul class="chandi_ul f_14  fl" style="width:380px;">

					<li class="green">淡黄染1/2/3级</li>

					<li style="width:240px;">

						<input type="text" class="dhr123_range" name="dhr123_range" value=""   />

					</li>

				</ul>

				<script>

					$(".dhr123_range").ionRangeSlider({

						type: "double",

						skin:"round",

							type: "double",

							min: 0,

							max: 100,

							from: '{$_GET["dhr123_from"]}' == '' ? 0 : '{$_GET["dhr123_from"]}',

							to: '{$_GET["dhr123_to"]}' == '' ? 100 : '{$_GET["dhr123_to"]}',

							step:0.1, 

							onFinish: function (data) { //拖动结束回调

								 // console.log(data)

								 $('input[name="dhr123_from"]').val(data.from);

								 $('input[name="dhr123_to"]').val(data.to);

							}

			 

					});

				</script>

				<ul class="chandi_ul f_14  fl" style="width:380px;">

					<li class="green">黄染1/2级</li>

					

					<li style="width:240px;"><input type="text" class="hr12_range" name="hr12_range" value=""   /></li>

				</ul>

				<script>

					$(".hr12_range").ionRangeSlider({

						type: "double",

						skin:"round",

							type: "double",

							min: 0,

							max: 100,

							from: '{$_GET["hr12_from"]}' == '' ? 0 : '{$_GET["hr12_from"]}',

							to: '{$_GET["hr12_to"]}' == '' ? 100 : '{$_GET["hr12_to"]}',

							step:0.1, 

							onFinish: function (data) { //拖动结束回调

								 // console.log(data)

									$('input[name="hr12_from"]').val(data.from);

								 $('input[name="hr12_to"]').val(data.to);

							}

			 

					});

				</script>

				

				<div class="cls"></div>

			</div>





			<ul class="chandi_ul f_14 mt_20 " id="jiaohuodi_ul" style="width:760px;">

				<li>交货地：</li>

				<li >

					<div id="jhd_click">

						<foreach name="jiaohuodi" item="v" key="k">

							<if condition="strpos($_GET['jhd'], '|'.$v['id'].'-|') nheq false">

								<a href="#." class="clr_3 mr_10 xuan_cur" myid="{$v.id}">{$v.name}</a>

							<else />

								<a href="#." class="clr_3 mr_10 " myid="{$v.id}">{$v.name}</a>

							</if>

						</foreach>

					</div>

				</li>

			</ul>

			



<div id="jhd_wraper" class="mt_10">

	<foreach name="jiaohuodi" item="v">

		<span id="jhd_{$v.id}">

			<if condition="$_GET['jhd2'] neq '' and $_GET['jhd2'] neq '|'">

				<foreach name="jhd2[$v['id']]" item="v2">

					<if condition="strpos($_GET['jhd2'],  '|'.$v['id'].'-'.$v2['id'].'-|') nheq false">

						<a href="#." class="clr_3 mr_10 xuan_cur f_14" pid="{$v.id}" myid="{$v2.id}">{$v2.name}</a>

					<else />

						<a href="#." class="clr_3 mr_10 f_14" pid="{$v.id}" myid="{$v2.id}">{$v2.name}</a>

					</if>

				</foreach>

			</if>

		</span>

	</foreach>

</div>





<script type="text/javascript">

//点击父交货地

	$('#jhd_click').on('click', 'a', function(){

		var that  = $(this);

		if(!that.hasClass('xuan_cur')){

			$.getJSON('{:U("Tools/getJhd")}', {id:that.attr('myid')}, function(d){

				// console.log(d)

				$.each(d, function(k,v){

					$('#jhd_'+that.attr('myid')).append('<a href="#." pid="'+v.pid+'" myid="'+v.id+'" class="mr_10 clr_3 f_14">'+v.name+'</a>');

				});

				that.addClass('xuan_cur');

			});

		}else{

			that.removeClass('xuan_cur');

		}



		$('#jhd_'+that.attr('myid')+' a[pid="'+that.attr('myid')+'"]').remove();

	});





	//点击子交货地

	$('#jhd_wraper').on('click', 'a', function(){

		var that  = $(this);

		if(!that.hasClass('xuan_cur')){

			that.addClass('xuan_cur');

		}else{

			that.removeClass('xuan_cur');

		}

	});

</script>



			<form action="{:U('index')}" id="fm1">

				<input id="act" name="act" value="" type="hidden" />

				<div class="dis_n">

					<input name="def" value="{$def}" />

					<!-- 父产地 -->

					<input name="cd" value="" />

					<!-- 子产地 -->

					<input name="cd2" value="" />

					<!-- 孙产地 -->

					<input name="cd3" value="" />

					

					<!-- 父交货地-->

					<input name="jhd" value="" placeholder="jhd" />

					<!-- 子交货地 -->

					<input name="jhd2" value=""  placeholder="jhd2"/>



					<textarea id="url" name="url"></textarea>

					<input name="leixing" value="{$_GET['leixing']}" />

					<input name="pihao_kunkao" value="{$_GET['pihao_kunkao']}" />

					<input name="changdu_from" value="{$_GET['changdu_from'] == '' ? 25 : $_GET['changdu_from']}" />

					<input name="changdu_to" value="{$_GET['changdu_to'] == '' ? 32 : $_GET['changdu_to']}"/>

					<input name="qiangli_from" value="{$_GET['qiangli_from'] == '' ? 25 : $_GET['qiangli_from']}"/>

					<input name="qiangli_to" value="{$_GET['qiangli_to'] == '' ? 32 : $_GET['qiangli_to']}"/>

					<input name="mazhi_from" value="<php>if($_GET['mazhi_from'] == ''){echo (2.5);}else{echo ($_GET['mazhi_from']);}</php>"/>

					<input name="mazhi_to" value="<php>if($_GET['mazhi_to'] == ''){echo (5.5);}else{echo ($_GET['mazhi_to']);}</php>"/>

					<input name="huichaolv_from" value="{$_GET['huichaolv_from'] == '' ? 0 : $_GET['huichaolv_from']}"/>

					<input name="huichaolv_to" value="{$_GET['huichaolv_to'] == '' ? 10 : $_GET['huichaolv_to']}"/>

					<input name="hanzalv_from" value="{$_GET['hanzalv_from'] == '' ? 0 : $_GET['hanzalv_from']}"/>

					<input name="hanzalv_to" value="{$_GET['hanzalv_to'] == '' ? 5 : $_GET['hanzalv_to']}"/>

					<input name="zhengqidu_from" value="{$_GET['zhengqidu_from'] == '' ? 77 : $_GET['zhengqidu_from']}"/>

					<input name="zhengqidu_to" value="{$_GET['zhengqidu_to'] == '' ? 90 : $_GET['zhengqidu_to']}"/>

					<input name="bm123_from" value="{$_GET['bm123_from'] == '' ? 0 : $_GET['bm123_from']}"/>

					<input name="bm123_to" value="{$_GET['bm123_to'] == '' ? 100 : $_GET['bm123_to']}"/>

					<input name="bm45_from" value="{$_GET['bm45_from'] == '' ? 0 : $_GET['bm45_from']}"/>

					<input name="bm45_to" value="{$_GET['bm45_to'] == '' ? 100 : $_GET['bm45_to']}"/>

					<input name="ddw123_from" value="{$_GET['ddw123_from'] == '' ? 0 : $_GET['ddw123_from']}"/>

					<input name="ddw123_to" value="{$_GET['ddw123_to'] == '' ? 100 : $_GET['ddw123_to']}"/>

					<input name="dhr123_from" value="{$_GET['dhr123_from'] == '' ? 0 : $_GET['dhr123_from']}"/>

					<input name="dhr123_to" value="{$_GET['dhr123_to'] == '' ? 100 : $_GET['dhr123_to']}"/>

					<input name="hr12_from" value="{$_GET['hr12_from'] == '' ? 0 : $_GET['hr12_from']}"/>

					<input name="hr12_to" value="{$_GET['hr12_to'] == '' ? 100 : $_GET['hr12_to']}"/>

				</div>

				<ul class="chandi_ul f_14 mt_20">

					<li>

						交货仓库：<input class="border1 f_14 clr_9 pad_5" placeholder="请输入仓库名称"  

							value="{$_GET['cangchumingcheng']}" name="cangchumingcheng" style="width:350px;" /> 

						轧花厂：<input class="border1 f_14 clr_9 pad_5" placeholder="请输入轧花厂名称或编号" 

							value="{$_GET['jiagongchang']}" name="jiagongchang"  style="width:350px;" /> 

						<input type="button" id="tijiaoClick" value="✓ 确认筛选　" class="f_14 clr_f bg_lan border0 pad_5" />

						<a href="#." id="add_dingzhi" style=" font-weight: 700;

						color: #F1830E;

						padding-left: 10px;

						font-size: 15px;">+添加到个性需求</a>

						<!-- <a href="#" class="clr_ora f_wei">+添加到需求定制</a> -->

					</li>

				</ul>



				<div class="dis_n">

					<input name="hot" value="{$_GET['hot']}" />

					<input name="zuiyou" value="{$_GET['zuiyou']}" />

					<input name="xiangtong"  value="{$_GET['xiangtong']}" />

					<input name="dan"  value="{$_GET['dan']}" />

					<input name="jicha"  value="{$_GET['jicha']}" />

				</div>

			</form>





			<script type="text/javascript">

			$('#add_dingzhi').click(function(){

				setForm();

				$('#url').val($('#fm1').serialize());

				$.post('{:U("rDingzhi")}',$('#fm1').serialize(), function(d){

					alert(d.msg);

					if(d.code==1){

						location.href="{:U('Center/dingzhi')}";

					}

				},'json');

			});



				function setForm(){

					//提交表单，而不是导出excel

					$('#act').val('');



					//所选的父产地

					var cd = '|';

					$('#chandi_click a').each(function(k, v){

						if($(this).hasClass('xuan_cur')){

							// cd += $(this).text() + '|';

							// console.log('v----'+$(this).attr('myid'));

							cd += $(this).attr('myid') + '-|';

						}

					});



					$('input[name="cd"]').val(cd);	



					

					//所选的子产地

					var cd2 = '|';

					$('#child_wraper a').each(function(k, v){

						if($(this).hasClass('xuan_cur')){

							// cd2 += $(this).text() + '|';

							// console.log('v----'+$(this).attr('myid'));

							cd2 += $(this).attr('pid')+'-'+$(this).attr('myid') + '-|';

						}

						

					});



					$('input[name="cd2"]').val(cd2);



					//所选的孙产地

					var cd3 = '|';

					$('#grandson_wraper a').each(function(k, v){

						if($(this).hasClass('xuan_cur')){

							// cd3+= $(this).text() + '|';

							// console.log('v----'+$(this).attr('myid'));

							cd3+= $(this).attr('gpid')+'-'+$(this).attr('pid')+'-'+$(this).attr('myid')+ '-|';

						}

						

					});



					$('input[name="cd3"]').val(cd3);





					//所选的父交货地

					var jhd = '|';

					$('#jhd_click a').each(function(k, v){

						if($(this).hasClass('xuan_cur')){

							// cd += $(this).text() + '|';

							// console.log('v----'+$(this).attr('myid'));

							jhd += $(this).attr('myid') + '-|';

						}

					});



					$('input[name="jhd"]').val(jhd);	



					

					//所选的子交货地

					var jhd2 = '|';

					$('#jhd_wraper a').each(function(k, v){

						if($(this).hasClass('xuan_cur')){

							// cd2 += $(this).text() + '|';

							// console.log('v----'+$(this).attr('myid'));

							jhd2 += $(this).attr('pid')+'-'+$(this).attr('myid') + '-|';

						}

						

					});



					$('input[name="jhd2"]').val(jhd2);

				}





				//提交表单

				$('#tijiaoClick').click(function(){

					setForm();

					$('#fm1').submit();

				});

			</script>

		</div>





		<div class="border1 bg_f mt_10 pad_10">

			<ul class="paixu_ul f_14 clearfix">

				<li class="clr_b f_wei">综合排序</li>

				<li id="zuiyou_order">

					<label class="mr_5" v="20">

						<input type="checkbox" class="ver_mid" <if condition="$_GET['zuiyou'] eq 20">checked</if>  /> 

						<span class="ver_mid">最优20批</span>

					</label>

					<label class="mr_5" v="50">

						<input type="checkbox" class="ver_mid"  <if condition="$_GET['zuiyou'] eq 50">checked</if> />

						<span class="ver_mid">最优50批</span>

					</label>

				</li>



				<li id="hot_order" style="margin-right: 15px;">

					<label class="mr_5 rad5" style="background: #e8e3df;padding:1px 5px 3px 5px;" >

						<input type="checkbox" class="ver_mid" <if condition="$_GET['hot'] eq 1">checked</if>  /> 

						<span class="ver_mid">热门</span>

					</label>

				</li>



				<li  style="border:0;"><a href="#." id="excelClick"><img src="__PUBLIC__/images/excel.png" class="dis_ib ver_mid" /></a></li>

				<li style="border:0;" class="f_14 clr_9" id="process"></li>

				<div class="fr">

					<li id="jicha_order"  style="border:0;">

						<label class="mr_5" v="yikoujia">

							<input type="checkbox" class="ver_mid" <if condition="$_GET['jicha'] eq 'yikoujia'">checked</if>/> <span class="ver_mid">一口价</span>

						</label>

						<label class="mr_5" v="jicha">

							<input type="checkbox" class="ver_mid" <if condition="$_GET['jicha'] eq 'jicha'">checked</if>/> <span class="ver_mid">基差点价</span>

						</label>

						

					</li>

					<li id="heyue_order"  style="border:0;">

						<label class="mr_5" v="05">

							<input type="checkbox" class="ver_mid" <if condition="$_GET['heyue'] eq '05'">checked</if>/> <span class="ver_mid">05合约</span>

						</label>

						<label class="mr_5" v="09">

							<input type="checkbox" class="ver_mid" <if condition="$_GET['heyue'] eq '09'">checked</if>/> <span class="ver_mid">09合约</span>

						</label>

						<span class="ver_mid">共计{$count}批</span>

					</li>

				</div>

			</ul>





<div class="mask_bg dis_n" id="mask_bg" >123123</div>

			<script type="text/javascript">

			var processBar2;

			$('#excelClick').click(function(){

				setForm();

				$('#act').val('excel');



				// $.get('{:U("index")}', $('#fm1').serialize(), function(d){

				//  	// console.log(d)

				//  });



				//  processBar2 = setInterval(sync_process, 2000);



				 $('#fm1').submit();

				

			});



			function sync_process(){

				console.log('111');



				$.get('{:U("Tools/getProcess")}',{id:Math.random()}, function(d){

					console.log('222');

					console.log(d);

					if(d.msg){

						$('#process').text(d.msg);

						if(d.msg == 'OK'){

							clearInterval(processBar2);

						}

					}

					

				})



				console.log('333');

			}



				$('#hot_order label').click(function(){

					if($(this).find('input[type="checkbox"]').is(':checked')){

						$('input[name="hot"]').val(1);

					}else{

						$('input[name="hot"]').val(0);

					}

					$('#fm1').submit();

				});



				$('#zuiyou_order label').click(function(){

					if($(this).find('input[type="checkbox"]').is(':checked')){

						$('input[name="zuiyou"]').val($(this).attr('v'));	

					}else{

						$('input[name="zuiyou"]').val('');

					}

					$('#fm1').submit();

				});



				$('#jicha_order label').click(function(){

					if($(this).find('input[type="checkbox"]').is(':checked')){

						$('input[name="jicha"]').val($(this).attr('v'));	

					}else{

						$('input[name="jicha"]').val('');

					}

					$('#fm1').submit();

				});

			</script>

		</div>

		

		<foreach name="lists" item="v">

			<div class="border1 bg_gr pad_20 f_16 clr_3 clearfix mt_10 pos_rela">

				<div class="fl" style="width:900px;" >

					<div>

						<!-- <div class="dis_ib cs1 "><div class="l1  dis_ib">5星</div><div class="l2  dis_ib">卖家</div></div> -->

						<a href="{:U('view',['id'=>$v['id']])}" class="clr_b f_wei">{$v.pihao_kunkao}</a>

						<span class="ml_10">{$v.leixing}</span>

						<span class="ml_10">{$v.jianshu}件</span>

						<span class="clr_9">加工厂 <span class="clr_3">{$v.jiagongchang}</span>

							&nbsp; <span class="clr_3">{$v.diqu_text}</span>

						</span>

					</div>



					<div class="mt_10 lh_30">

						<span class="clr_9">颜色 <span class="clr_3">{$v.yanseji_pinji}</span></span>

						<span class="clr_9">马值 <span class="clr_3">{$v.mazhi}</span></span>

						<span class="clr_9">长度 <span class="clr_3">{$v.changdu}</span></span>

						<span class="clr_9">强力 <span class="clr_3">{$v.qiangli}</span></span>

						<span class="clr_9">含杂<span class="clr_3">{$v.hanzalv}</span></span>

						<span class="clr_9">回潮 <span class="clr_3">{$v.huichaolv}</span></span>

						<span class="clr_9">整齐度<span class="clr_3">{$v.zhengqidu}</span></span><br />



						

					</div>

					<div class="mt_10">

						<span class="clr_9">仓储名称 <span class="clr_3">{$v.cangchumingcheng}</span></span>

						<span class="clr_9" style="margin:0 15px;">

							<span class="clr_3">{$v.diqu_text_cangku}</span>

						</span>

						<span class="clr_9">更新时间： <span class="clr_3">{$v.add_time|date="Y/m/d H:i", ###}</span></span>

					</div>

				</div>



				<div class="fr clearfix f_16 al_ct " style="width:250px;">

					<div class="clr_9 ">参考合约： <span>{$v.dianjiaheyue}</span></div>

					<div class="clr_9 mt_10">基差： <span class="clr_3 f_20">{$v.jicha}</span></div>

					<a href="#." class="f_16 zy_box clr_ora dis_ib mt_10 add_favor" myid="{$v.id}" >

						<span class="ver_mid">收藏</span>

					</a>

				</div>

			</div>

		</foreach>





		<script type="text/javascript">

			$('.add_favor').click(function(){

				$.getJSON('{:U("addFavor")}', {id:$(this).attr('myid')}, function(d){

					alert(d.msg);

				});

			});

		</script>





		<div class="pager mt_20 ">{$page}</div>

		<div class="pad_30"></div>





	</div>







	<div class="footer mt_30 min_w">

		<include file="Public:footer" />

	</div>





</body>





</html>
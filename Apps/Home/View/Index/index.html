<!DOCTYPE html>
<html>
<head>
<title>棉花棠</title>
<meta name="keywords" content="棉花棠" />
<meta name="description" content="棉花棠" />
<include file="Public:header" />
<style type="text/css">
.chaoshi_tbl th{text-align: center;}
</style>
</head>
<body class="bg_f2">
<include file="Public:top" />
<include file="Public:nav" />
	<div class="m_auto">
		<div class="bar">每日精选</div>
		<div style="overflow-x: auto;">
			<table class="chaoshi_tbl f_16 clr_3 w_100p mt_10 bg_f"  >
				<tr style="background: #28afe5;text-align: center;" class="clr_f">
					<th>批号</th>
					<th>类型</th>
					<th>颜色级</th>
					<th>马值</th>
					<th>长度</th>
					<th>强力</th>
					<th>含杂</th>
					<th>回潮</th>
					<th>公重</th>
					<th>整齐度</th>
					<th>加工厂</th>
					<th>仓库</th>
					<th>基差</th>
					<th>点价合约</th>
				</tr>
				<foreach  name="chaoshi" item="v">
					<tr style="text-align:center;">
						<td>
							<!--
								<div class="dis_ib mj1 f_14 ver_mid">
									<div class="h1">5星</div>
									<div class="h2">卖家</div>
								</div>
							-->
							<div class="dis_ib ver_mid"><a href="{:U('Chaoshi/view', ['id'=>$v['id']])}" class="clr_b f_wei">{$v.pihao_kunkao}</a></div>
						</td>
						<td>{$v.leixing}</td>
						<td ><div title="{$v.yanseji_pinji}">{$v.yanseji_pinji|msubstr2=0,6}</div></td>
						<td>{$v.mazhi}</td>
						<td>{$v.changdu}</td>
						<td>{$v.qiangli}</td>
						<td>{$v.hanzalv}</td>
						<td>{$v.huichaolv}</td>
						<td>{$v.gongzhong}</td>
						<td>{$v.zhengqidu}</td>
						<td ><div title="{$v.jiagongchang}">{$v.jiagongchang|msubstr2=0,6}</div></td>
						<td ><div title="{$v.cangchumingcheng}">{$v.cangchumingcheng|msubstr2=0,6}</div></td>
						<td>{$v.jicha}</td>
						<td>{$v.dianjiaheyue}</td>
					</tr>
				</foreach>
			</table>
		</div>
	</div>
 <div class="swiper-container pos_rela min_w"  style="    width: 1200px;
    margin: 0 auto;">
			<div class="swiper-wrapper">
				<foreach name="bann" item="v">
					<div class="swiper-slide" style="background: url(__ROOT__/Attached/{$v.thumbs}) no-repeat center 0;height:450px;background-size: auto 100%;">
						<if condition="$v.url neq ''">
							<a href="{$v.url}" target="_blank" class="dis_b" style="height:450px;"></a>
						</if>
					</div>
				</foreach>
			</div>
			<!-- Add Pagination -->
				<div class="swiper-pagination"></div>
				<!-- Add Arrows -->
				<div class="swiper-button-prev"></div>
				<div class="swiper-button-next"></div>
	</div>
	<script>
		var swiper = new Swiper('.swiper-container', {
			nextButton: '.swiper-button-next',
			prevButton: '.swiper-button-prev',
			pagination: '.swiper-pagination',
			// paginationType: 'fraction',
			loop : true,
			autoplay : 4000,
		});
	</script>
	<div class="m_auto">
		<div class="bar ">个性需求</div>
		<!-- <a href="{:U('Center/dingzhi')}" class="mt_10 dis_b"><img src="__PUBLIC__/images/xq.jpg" class="dis_b" /></a> -->
		<foreach name="dingzhi" item="v">
			<div class="border1 bg_f pad_20 f_16 clr_3 clearfix mt_10">
			<div class="fl dz_left" style="width:960px;" >
			<!-- {$v.url|urldecode=###} -->
				<a href="#" class="clr_b f_wei mr_10">{$v.dingzhi_no}</a>
				<span class="clr_9 mr_10">类型：<span class="f_wei clr_3"> {$v.leixing}</span></span>
				<span class="clr_9 mr_10">年度 ：<span class="clr_3">{$v.pihao_kunkao}</span></span>
				<div class="mt_10 lh_30">
					<span class="clr_9 mr_10">长 度：<span class="clr_3">{$v.changdu}</span></span>
					<span class="clr_9 mr_10">马值 ：<span class="clr_3">{$v.mazhi}</span></span>
					<span class="clr_9 mr_10">强力 ：<span class="clr_3">{$v.qiangli}</span></span>
					<span class="clr_9 mr_10">回潮 ：<span class="clr_3">{$v.huichaolv}</span></span>
					<span class="clr_9 mr_10">整齐度：<span class="clr_3">{$v.zhengqidu}</span></span>
					<span class="clr_9 mr_10">含杂：<span class="clr_3">{$v.hanzalv}</span></span><br />
					<span class="clr_9 mr_10">白棉1/2/3级 ：<span class="clr_3">{$v.mazhi}</span></span>
					<span class="clr_9 mr_10">白棉4/5级 ：<span class="clr_3">{$v.qiangli}</span></span>
					<span class="clr_9 mr_10">淡点污1/2/3级 ：<span class="clr_3">{$v.huichaolv}</span></span>
					<span class="clr_9 mr_10">淡黄染1/2/3级：<span class="clr_3">{$v.zhengqidu}</span></span>
					<span class="clr_9 mr_10">黄染1/2级：<span class="clr_3">{$v.hanzalv}</span></span><br />
					<span class="clr_9 mr_10">轧花厂： <span class="clr_3">{$v.jiagongchang}</span></span>
					<span class="clr_9 mr_10">交货仓库：<span class="clr_3">{$v.cangchumingcheng}</span></span><br />
				</div>
			</div>
			<div class="fr dz_right clearfix" style="width:160px;">
				<div style="text-align: right;">
					<!-- <a href="#" class="edit_btn f_14 dis_b">修改</a> -->
					<a href="{:U('delDingzhi',['id'=>$v['id']])}" class="del_btn f_14 dis_ib mt_10" onclick="return confirm('确认删除？');">删除</a><br />
					<!-- <div class="f_16 clr_9">平台最优价格 <span class="f_wei clr_ora f_22">15700</span></div> -->
					<a href="{$v.url}" class="f_16 zy_box clr_ora  pos_rela mt_10 dis_ib">
						<!-- <div class="icon2">0</div> -->
						<img src="__PUBLIC__/images/icon.png" class="ver_mid" /> <span class="ver_mid">查看资源</span>
					</a>
				</div>
			</div>
		</div>
		</foreach>
	</div>
<!--
	<div class="m_auto clearfix">
		<div class="fl" style="width:590px;">
			<div class="bar">关于我们</div>
			<div class="border1 bg_f pad_15 mt_10 f_16" style="min-height: 150px;">
				{$our.content}
			</div>
		</div>
		<div class="fr" style="width:590px;">
			<div class="bar">联系我们</div>
			<div class="border1 bg_f pad_15 mt_10 f_16" style="min-height: 150px;">
				{$contact.content}
			</div>
		</div>
	</div>
 -->
	<div class="footer mt_30 min_w">
		<include file="Public:footer" />
	</div>
</body>
</html>
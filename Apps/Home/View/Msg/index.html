<!DOCTYPE html>
<html lang="en">
	<head>
		<title>在线留言</title>

<include file="Public:header" />
	</head>
	<body>
		<include file="Public:navi" />
			<div class="dislpayArrow"><a class="pngfix" href="javascript:void(0);" onclick="displaynavbar(this)"></a></div>
		<div class="content">
			<div class="banner">
				<img src="__ROOT__/Attached/{$bann[0].thumbs}" alt="">
			</div>
			<div class="w1200">
			<div class="onLine-xy col12">
					<div class="cont_title wow bounceInUp"  data-wow-duration="2s" data-wow-delay="0.5s" style="width: 450px;">
							<p>把您的需求告诉我们 <span>请在此留下您的宝贵意见</span></p>
					</div>
					<div class="of_onLine col12 wow bounceInUp"  data-wow-duration="2.5s" data-wow-delay="0.5s">
							<p>我们时刻关注客户需求，并做好随时能够与您洽谈业务的准备。
									欢迎您来电咨询，索取专业资料或者预约见面时间。</p>
						<form  id="fm1">
							<div class="col12">
								<div class="form-group col5">
									<label for=""><span class="red">*</span> 姓名：</label>
									<input type="text" name="name" id="name" class="form-control" placeholder="请输入姓名">
								</div>
								<div class="form-group col5 fr">
									<label for=""> 城市：</label>
									<input type="text" name="city"   id="city" class="form-control" placeholder="请输入城市">
								</div>
							</div>
							<div class="col12">
									<div class="form-group col5">
										<label for=""><span class="red">*</span> 邮箱：</label>
										<input type="text" name="email"  id="email"  class="form-control" placeholder="请输入您的邮箱">
									</div>
									<div class="form-group col5 fr">
										<label for=""><span class="red">*</span> 手机：</label>
										<input type="text" name="mobile"  id="mobile"  class="form-control" placeholder="请输入手机号码">
									</div>
								</div>
								<div class="col12">
									<div class="form-group col12">
										<label for="" style="position: relative;top:-70px";><span class="red">*</span> 内容：</label>
										<textarea name="content" id="content"   class="form-control" rows="3" placeholder=""></textarea>
									</div>
								</div>
							<div class="col12">
									<div class="form-group col6">
										<label for=""> 验证码：</label>
										<input type="text"  name="captcha" id="captcha"  class="form-control" style="width: 126px;" placeholder="">
										<a href="javascript:;"  class="code"><img src="{:U('Tools/verify')}" id="verify_img" alt=""></a>
									</div>
								</div>
								<div class="col12" style="text-align: center;margin-top:37px;">
									<button type="reset" class="btn btn-yl">重置</button>
									<button type="submit" class="btn btn-default">提交</button>
								</div>
						</form>	


						<script>
						$('#verify_img').click(function(){
							$(this).attr('src', "{:U('Tools/verify')}");
						});
						$('#fm1').submit(function(e){
							e.preventDefault();

							if(/^[ ]*$/.test($('#name').val())){
								alert('请输入姓名');
								return false;
							}

							if(!/^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/.test($('#email').val())){
								alert('请输入正确的邮箱');
								return false;
							}


							if(!/^1\d{10}$/.test($('#mobile').val())){
								alert('请输入正确的手机号');
								return false;
							}


							if(/^[ ]*$/.test($('#content').val())){
								alert('请输入内容');
								return false;
							}


							if(/^[ ]*$/.test($('#captcha').val())){
								alert('请输入验证码');
								return false;
							}


							$.post('{:U("runMsg")}', $('#fm1').serialize(), function(d){
								alert(d.msg);
								if(d.code==1){
									history.go(0);
								}else{
									$('#verify_img').click();
								}
							},'json');
						});
						</script>
					</div>
					
			</div>
			
		</div>
			<include file="Public:footer" />
		</div>
	</body>
</html>

<!DOCTYPE html>

<html>

<head>

	<title>棉花棠</title>

	<meta name="keywords" content="棉花棠" />

	<meta name="description" content="棉花棠" />

	<include file="Public:header" />



</head>



<body class="bg_f2">

	<include file="Public:top" />

	

	<div class="login_bg min_w">

		<div class="m_auto clearfix">

			<div class="login_box bg_f fr mt_70">

				<div class="login_box_tt f_18 padl_30">用户登录</div>	

				<div class="padl_40">

					<form id="fm1">

						<div class="border1 pad_10 mt_30">

							<img src="__PUBLIC__/images/user.png" class="ver_mid" />

							<input placeholder="手机号" name="username" id="username" value="{$username_c}" class="f_16 clr_9  border0 ver_mid ml_10" />

						</div>

						<div class="border1 pad_10 mt_30">

							<img src="__PUBLIC__/images/password.png" class="ver_mid" />

							<input placeholder="密码"  name="password" id="password" value="{$password_c}"  type="password" class="f_16 clr_9  border0 ver_mid ml_10" />

						</div>

						

		<!-- 				<div class="mt_30">

							<input placeholder="验证码"  name="captcha" id="captcha" class="f_16 clr_9  ver_mid border1 pad_10" style="width:120px;" />

							<img src="{:U('Tools/verify')}" class="ver_mid" id="verify" />

						</div> -->

						<div class=" padt_30">

							<input type="submit" value="登录" class="f_16 clr_f bg_b w_100p border0 padt_10" />

						<div class=" f_14" style="margin-top:8px; float:left;">

							<label>

								<input type="checkbox" name="member" class="ver_mid" value="1" <if condition="$member_c eq 1">checked</if>  > 

								<span class="ver_mid">记住密码</span>

							</label>

						</div>

							<div class="f_14 al_rt mt_10">

								<a href="{:U('forget')}" class="clr_b">忘记密码？</a>

								<a href="{:U('reg')}" class="clr_3">新用户注册</a>

							</div>

						</div>

					</form>

					

					<script type="text/javascript">

						$('#verify').click(function(){

							$(this).attr('src', "{:U('Tools/verify')}");

						});



						$('#fm1').submit(function(e){

							e.preventDefault();

							if(!/^1\d{10}$/.test($('#username').val())){

								alert('请输入正确的手机号码');

								$('#username').focus();

								return false;

							}





							if(/^[ ]*$/.test($('#password').val())){

								alert('请输入密码');

								$('#password').focus();

								return false;

							}



							if(/^[ ]*$/.test($('#captcha').val())){

								alert('请输入验证码');

								$('#captcha').focus();

								return false;

							}





							$.post('{:U("runLogin")}', $('#fm1').serialize(), function(d){

								if(d.code== 1){

									location.href=d.url;

								}else{

									alert(d.msg);

									$('#verify').click();

								}

							},'json');

						});

					</script>



				</div>

			</div>

		</div>

	</div>





<div class="min_w">

<include file="Public:footer" />

</div>





</body>





</html>
<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">

<title>后台登录</title>

<meta name="renderer" content="webkit|ie-comp|ie-stand">

<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">

<meta http-equiv="Cache-Control" content="no-siteapp" />

<link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />

<link rel="stylesheet" href="/Public/xadmin/css/font.css">

<link rel="stylesheet" href="/Public/xadmin/css/xadmin.css">

<script type="text/javascript" src="/Public/xadmin/js/jquery.min.js"></script>

<script src="/Public/xadmin/lib/layui/layui.js" charset="utf-8"></script>

<script type="text/javascript" src="/Public/xadmin/js/xadmin.js"></script>

<link rel="stylesheet" href="/Public/css/back2.css">

<link rel="stylesheet" href="/Public/font-awesome/css/font-awesome.min.css">


</head>
<body>
<div class="x-body">
	<button class="layui-btn add_item" my_url="<?php echo U('add');?>"> <i class="fa fa-plus-circle" aria-hidden="true"></i> 添加管理员</button>
	<table class="tbl2 bg_f w_100 rad_10 mt_10 clr_79 w_100" >
	<thead>
		<tr class="al_lt"><th >名称</th><th width="200">操作</th></tr>
	</thead>
		<?php if(is_array($lists)): foreach($lists as $key=>$v): ?><tr>
				<td ><?php echo ($v["name"]); ?></td>
				<td>
					<a href="javascript:;" my_url="<?php echo U('edit', array('id'=>$v['id']));?>" class="edit_item bg_g block_a " title="编辑" >
						<i class="fa fa-pencil-square-o" aria-hidden="true"  style="font-size: 14px;color:#fff;"></i>
					</a>
					&nbsp;
					<a href="javascript:;" my_url="<?php echo U('del', array('id'=>$v['id']));?>" class="del_item bg_r block_a " title="删除" >
						<i class="fa fa-trash-o" aria-hidden="true" style="font-size: 14px;color:#fff;"></i>
					</a>
				</td>
			</tr><?php endforeach; endif; ?>
	</table>
</div>
<script>
	$('.add_item').click(function(){
		x_admin_show('添加管理员',$(this).attr('my_url'));
	});
	$('.edit_item').click(function(){
		x_admin_show('编辑',$(this).attr('my_url'));
	});
	$('.del_item').click(function(){
		var aa = $(this);
		layer.confirm('确认删除？', {icon: 3, title:'提示'}, function(index){
			$.getJSON(aa.attr('my_url'), function(d){
				if(d.code==1){
					history.go(0);
				}
			});
			layer.close(index);
		});
	});
</script>
</body>
</html>
<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">

<title>后台登录</title>

<meta name="renderer" content="webkit|ie-comp|ie-stand">

<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">

<meta http-equiv="Cache-Control" content="no-siteapp" />

<link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />

<link rel="stylesheet" href="/Public/xadmin/css/font.css">

<link rel="stylesheet" href="/Public/xadmin/css/xadmin.css">

<script type="text/javascript" src="/Public/xadmin/js/jquery.min.js"></script>

<script src="/Public/xadmin/lib/layui/layui.js" charset="utf-8"></script>

<script type="text/javascript" src="/Public/xadmin/js/xadmin.js"></script>

<link rel="stylesheet" href="/Public/css/back2.css">

<link rel="stylesheet" href="/Public/font-awesome/css/font-awesome.min.css">


</head>
<body>
<div class="x-body">
	<form id="fm1" class="layui-form"  method="post">
		<table class="layui-table"  style="width:800px;">
		<thead>
			<tr><th colspan="2">添加管理员</th></tr>
		</thead>
			<tr><td style="width:100px;">管理员名称：</td><td><input name="name"  lay-verify="required" class="layui-input"/> </td></tr>
			<tr><td>密码：</td><td><input type="password" name="pwd"  lay-verify="required" class="layui-input"/> </td></tr>
				<tr style="display: none;"><td>权限列表：</td><td>
					<?php if(is_array($leftNav)): foreach($leftNav as $key=>$v): ?><div style="margin-top:10px;font-size: 14px;color: #333;"><?php echo ($v["name"]); ?></div>
							<?php if(is_array($v["child"])): foreach($v["child"] as $key=>$v2): ?><div style="margin:5px 0 0 0;float:left;">
									<input type="checkbox" name="cat1_<?php echo ($v["id"]); ?>[]" title="<?php echo ($v2["name"]); ?>" value="<?php echo ($v2["id"]); ?>" checked  />
								</div><?php endforeach; endif; ?>
							<div style="clear:both;"></div><?php endforeach; endif; ?>
				</td></tr>
			<tr><td></td><td><input type="submit" value=" 保存 " lay-submit lay-filter="formDemo"   class="layui-btn" /></td></tr>
		</table>
	</form>
</div>
<script>
	layui.use('form', function(){
		  var form = layui.form;
		  //监听提交
		  form.on('submit(formDemo)', function(data){
		    $.post("<?php echo U('runAdd');?>",$('#fm1').serialize(), function(d){
		    	if(d.code == 1){
		    		layer.alert("保存成功", {icon: 6},function () {
		    			window.parent.location.reload();
	    				var index = parent.layer.getFrameIndex(window.name);
		                //关闭当前frame
		                parent.layer.close(index);
		            });
		    	}else{
		    		layer.alert(d.msg, {icon:2});
		    	}
		    },'json');
		      return false;
		  });
	});
</script>
</body>
</html>
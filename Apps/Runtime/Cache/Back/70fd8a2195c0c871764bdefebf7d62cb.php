<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">

<title>后台登录</title>

<meta name="renderer" content="webkit|ie-comp|ie-stand">

<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">

<meta http-equiv="Cache-Control" content="no-siteapp" />

<link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />

<link rel="stylesheet" href="/Public/xadmin/css/font.css">

<link rel="stylesheet" href="/Public/xadmin/css/xadmin.css">

<script type="text/javascript" src="/Public/xadmin/js/jquery.min.js"></script>

<script src="/Public/xadmin/lib/layui/layui.js" charset="utf-8"></script>

<script type="text/javascript" src="/Public/xadmin/js/xadmin.js"></script>

<link rel="stylesheet" href="/Public/css/back2.css">

<link rel="stylesheet" href="/Public/font-awesome/css/font-awesome.min.css">


<link rel="stylesheet" href="/Public/kinder/themes/default/default.css" />
<link rel="stylesheet" href="/Public/kinder/plugins/code/prettify.css" />
<script charset="utf-8" src="/Public/kinder/kindeditor-min.js"></script>
<script charset="utf-8" src="/Public/kinder/lang/zh_CN.js"></script>
<script charset="utf-8" src="/Public/kinder/plugins/code/prettify.js"></script>
<script type="text/javascript" src="/Public/js/My97DatePicker/WdatePicker.js"></script>
<script charset="utf-8" src="/Public/js/jquery.form.js"></script>
<script type="text/javascript" src="/Public/js/jquery.validate.min.js"></script>
<style>
.ke-toolbar:{background: #fff !important;}
</style>
<script>
	KindEditor.ready(function(K) {
		var editor1 = K.create('textarea[class="htmltext"]', {
			cssPath : '/Public/kinder/plugins/code/prettify.css',
			uploadJson : '/Public/kinder/upload_json.php',
			fileManagerJson : '/Public/kinder/file_manager_json.php',
			allowFileManager : true,
			afterBlur: function () { this.sync(); },//注意这里（异步提交时需要同步）
			afterCreate: function () {
				var self = this;
				self.sync();//把富文本编辑器的内容放到 文本域里面去。
			}
		});
		prettyPrint();
	});
$(function(){
	//上传
		$('#uploadForm').ajaxForm({
			dataType: 'json',
				success: function(data){
						if(data.up_msg=='OK'){
							data.up_msg = '上传成功！';
							$('#img_wrap').html('<img src="/Attached/'+data.img_url+'" style="width:80px;height:80px;" />');
							$('#thumbs').val(data.img_url);
						}
						$('.tips').text(data.up_msg);
				},
				beforeSubmit:function(){
				}
			});
			$('.go_up').click(function(){
					$('#thumbs_hide').click();
			});
			$('#thumbs_hide').change(function(){
				$('#uploadForm').submit();
			});
	//上传
		$('#uploadForm2').ajaxForm({
			dataType: 'json',
				success: function(data){
						if(data.up_msg=='OK'){
							data.up_msg = '上传成功！';
							$('#img_wrap2').html('<img src="/Attached/'+data.img_url+'" style="width:80px;height:80px;" />');
							$('#thumbs2').val(data.img_url);
						}
						$('.tips2').text(data.up_msg);
				},
				beforeSubmit:function(){
				}
			});
			$('.go_up2').click(function(){
					$('#thumbs_hide2').click();
			});
			$('#thumbs_hide2').change(function(){
				$('#uploadForm2').submit();
			});
});
</script>
</head>
	<div class="x-body">
			<form id="fm1" class="layui-form" method="post">
				<input type="hidden" name="type" value="<?php echo ($_GET['type']); ?>" />
				<input type="hidden" name="cat_id" value="<?php echo ($_GET['cid']); ?>" />
				<table class="tbl3 bg_f w_100 rad_10 mt_10 clr_79">
					<tr><td style="width:100px;">名称</td><td><input lay-verify="required"  name="title" class="layui-input"/></td></tr>
					<!-- <tr><td style="width:100px;">描述</td><td><textarea name="summary" class="layui-textarea"></textarea></td></tr> -->

						<tr><td style="width:100px;">地区</td><td>
							<select name="level1" id="level1" lay-ignore style="width:200px;height: 35px;">
								<option value="">请选择</option>
								<?php if(is_array($xj_level1)): foreach($xj_level1 as $key=>$v): ?><option value="<?php echo ($v["id"]); ?>"><?php echo ($v["name"]); ?></option><?php endforeach; endif; ?>
							</select>
							<select name="level2" id="level2" lay-ignore style="width:200px;height: 35px;">
								<option value="">请选择</option>
							</select>
<!-- 							<select name="level3" id="level3" lay-ignore style="width:200px;height: 35px;">
								<option value="">请选择</option>
							</select> -->
						</td></tr>
<!-- 						<tr>
							<td >公司LOGO</td><td>
								<span id="img_wrap"></span>
								<span>
									<input name="thumbs" id="thumbs" type="hidden"  />
									<input type="button" class="go_up layui-btn" value="上传"  />
									<span class="tips">2M以内、jpg/png </span>
								</span>
							</td>
						</tr>
						<tr>
							<td >名片二维码</td><td>
								<span id="img_wrap2"></span>
								<span>
									<input name="thumbs2" id="thumbs2" type="hidden"  />
									<input type="button" class="go_up2 layui-btn" value="上传"  />
									<span class="tips2">2M以内、jpg/png </span>
								</span>
							</td>
						</tr> -->
						<!-- <tr ><td>内容</td><td><textarea class="htmltext w_100" name="content" style="height:500px;visibility:hidden;" ></textarea></td></tr>	 -->
					<tr><td></td><td><input type="submit" value="添加" lay-submit lay-filter="formDemo" class="layui-btn" /></td></tr>
				</table>
			</form>

<script>
$('#level1').change(function(){
	if($(this).val()){
		$.getJSON('<?php echo U("getCat");?>', {id:$(this).val()}, function(d){
			console.log(d)
			$("#level2 option:gt(0)").remove();
			$("#level3 option:gt(0)").remove();
			$.each(d, function(k,v){
				$('#level2').append('<option value="'+v.id+'">'+v.name+'</option>');
			});
		});
	}
});


$('#level2').change(function(){
	if($(this).val()){
		$.getJSON('<?php echo U("getCat");?>', {id:$(this).val()}, function(d){
			console.log(d)
			$("#level3 option:gt(0)").remove();
			$.each(d, function(k,v){
				$('#level3').append('<option value="'+v.id+'">'+v.name+'</option>');
			});
		});
	}
});

	layui.use('laydate', function(){
		var laydate = layui.laydate;
		//执行一个laydate实例
		laydate.render({
			elem: '#pub_date' //指定元素
		});
	});

	 layui.use('form', function(){
		var form = layui.form;
		//监听提交
		form.on('submit(formDemo)', function(data){

			if($('#level1').val()=='' || $('#level2').val()=='' || $('#level3').val()==''){
				alert('请选择地区');
				return false;
			}

			$.post("<?php echo U('runAdd');?>",$('#fm1').serialize(), function(d){
				if(d.code == 1){
					layer.alert("增加成功", {icon: 6},function () {
						window.parent.location.reload();
							var index = parent.layer.getFrameIndex(window.name);
										//关闭当前frame
										parent.layer.close(index);
							});
				}
			},'json');
			return false;
		});
	});
</script>
			<form style="display: none;" id="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('Tools/runUpload');?>">
				<input type="file" name="thumbs_hide" id="thumbs_hide" />
			</form>
			<form style="display: none;" id="uploadForm2" enctype="multipart/form-data"  method="post" action="<?php echo U('Tools/runUpload');?>">
				<input type="file" name="thumbs_hide" id="thumbs_hide2" />
			</form>
	</div>
</body>
</html>
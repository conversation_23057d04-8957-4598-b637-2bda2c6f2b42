<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">

<title>后台登录</title>

<meta name="renderer" content="webkit|ie-comp|ie-stand">

<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">

<meta http-equiv="Cache-Control" content="no-siteapp" />

<link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />

<link rel="stylesheet" href="/Public/xadmin/css/font.css">

<link rel="stylesheet" href="/Public/xadmin/css/xadmin.css">

<script type="text/javascript" src="/Public/xadmin/js/jquery.min.js"></script>

<script src="/Public/xadmin/lib/layui/layui.js" charset="utf-8"></script>

<script type="text/javascript" src="/Public/xadmin/js/xadmin.js"></script>

<link rel="stylesheet" href="/Public/css/back2.css">

<link rel="stylesheet" href="/Public/font-awesome/css/font-awesome.min.css">


</head>
<body>
    <div class="x-body">
        <form class="layui-form" id="form1">
            <input type="hidden" name="id" value="<?php echo ($field["id"]); ?>">
            
            <div class="layui-form-item">
                <label for="field_code" class="layui-form-label">
                    <span class="x-red">*</span>字段代码
                </label>
                <div class="layui-input-inline">
                    <input type="text" id="field_code" name="field_code" required="" lay-verify="required"
                           autocomplete="off" class="layui-input" value="<?php echo ($field["field_code"]); ?>" placeholder="如：weight, region">
                </div>
                <div class="layui-form-mid layui-word-aux">
                    <span class="x-red">*</span>英文字母和下划线，不能重复
                </div>
            </div>
            
            <div class="layui-form-item">
                <label for="field_name" class="layui-form-label">
                    <span class="x-red">*</span>字段名称
                </label>
                <div class="layui-input-inline">
                    <input type="text" id="field_name" name="field_name" required="" lay-verify="required"
                           autocomplete="off" class="layui-input" value="<?php echo ($field["field_name"]); ?>" placeholder="如：重量, 地区">
                </div>
                <div class="layui-form-mid layui-word-aux">
                    <span class="x-red">*</span>显示给用户的名称
                </div>
            </div>
            
            <div class="layui-form-item">
                <label for="field_type" class="layui-form-label">
                    <span class="x-red">*</span>字段类型
                </label>
                <div class="layui-input-inline">
                    <select name="field_type" lay-verify="required" lay-filter="field_type">
                        <option value="">请选择字段类型</option>
                        <?php if(is_array($field_types)): foreach($field_types as $k=>$v): ?><option value="<?php echo ($k); ?>" <?php if($field['field_type'] == $k): ?>selected<?php endif; ?>><?php echo ($v); ?></option><?php endforeach; endif; ?>
                    </select>
                </div>
                <div class="layui-form-mid layui-word-aux">
                    数值型支持范围，选择型需配置选项
                </div>
            </div>
            
            <div class="layui-form-item">
                <label for="field_unit" class="layui-form-label">字段单位</label>
                <div class="layui-input-inline">
                    <input type="text" id="field_unit" name="field_unit"
                           autocomplete="off" class="layui-input" value="<?php echo ($field["field_unit"]); ?>" placeholder="如：kg, %, mm">
                </div>
                <div class="layui-form-mid layui-word-aux">
                    可选，用于显示
                </div>
            </div>
            
            <div class="layui-form-item" id="select_options_div" <?php if($field['field_type'] != 'select'): ?>style="display:none;"<?php endif; ?>>
                <label for="select_options" class="layui-form-label">选择项配置</label>
                <div class="layui-input-block">
                    <textarea name="select_options" placeholder="每行一个选项，如：&#10;A级&#10;B级&#10;C级" class="layui-textarea"><?php echo ($field["select_options_text"]); ?></textarea>
                </div>
                <div class="layui-form-mid layui-word-aux">
                    当字段类型为选择型时必填
                </div>
            </div>
            
            <div class="layui-form-item">
                <label for="is_range" class="layui-form-label">支持范围</label>
                <div class="layui-input-block">
                    <input type="radio" name="is_range" value="1" title="支持" <?php if($field['is_range'] == 1): ?>checked=""<?php endif; ?>>
                    <input type="radio" name="is_range" value="0" title="不支持" <?php if($field['is_range'] == 0): ?>checked=""<?php endif; ?>>
                </div>
                <div class="layui-form-mid layui-word-aux">
                    支持范围的字段可以设置最小值和最大值
                </div>
            </div>
            
            <div class="layui-form-item">
                <label for="sort_order" class="layui-form-label">排序权重</label>
                <div class="layui-input-inline">
                    <input type="number" id="sort_order" name="sort_order" value="<?php echo ($field["sort_order"]); ?>"
                           autocomplete="off" class="layui-input">
                </div>
                <div class="layui-form-mid layui-word-aux">
                    数值越小排序越靠前
                </div>
            </div>
            
            <div class="layui-form-item">
                <label for="is_enabled" class="layui-form-label">启用状态</label>
                <div class="layui-input-block">
                    <input type="radio" name="is_enabled" value="1" title="启用" <?php if($field['is_enabled'] == 1): ?>checked=""<?php endif; ?>>
                    <input type="radio" name="is_enabled" value="0" title="禁用" <?php if($field['is_enabled'] == 0): ?>checked=""<?php endif; ?>>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label for="" class="layui-form-label"></label>
                <button class="layui-btn" lay-filter="edit" lay-submit="">保存</button>
            </div>
        </form>
    </div>
    
    <script>
        layui.use(['form','layer'], function(){
            $ = layui.jquery;
            var form = layui.form,
                layer = layui.layer;

            //监听字段类型选择
            form.on('select(field_type)', function(data){
                if(data.value == 'select'){
                    $('#select_options_div').show();
                } else {
                    $('#select_options_div').hide();
                }
            });

            //自定义验证规则
            form.verify({
                field_code: function(value){
                    if(!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(value)){
                        return '字段代码只能包含字母、数字和下划线，且不能以数字开头';
                    }
                },
                field_name: function(value){
                    if(value.length < 2){
                        return '字段名称至少得2个字符';
                    }
                }
            });

            //监听提交
            form.on('submit(edit)', function(data){
                console.log(data);
                //发异步，把数据提交给php
                $.ajax({
                    url: "<?php echo U('runEdit');?>",
                    type: 'POST',
                    data: data.field,
                    dataType: 'json',
                    success: function(res){
                        if(res.code == 1){
                            layer.alert("修改成功", {icon: 1}, function(){
                                // 获得frame索引
                                var index = parent.layer.getFrameIndex(window.name);
                                //关闭当前frame
                                parent.layer.close(index);
                                // 刷新父页面
                                parent.location.reload();
                            });
                        }else{
                            layer.alert(res.msg, {icon: 2});
                        }
                    },
                    error: function(){
                        layer.alert("请求失败", {icon: 2});
                    }
                });
                return false;
            });
        });
    </script>
</body>
</html>
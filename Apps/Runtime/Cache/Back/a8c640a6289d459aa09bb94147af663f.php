<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">

<title>后台登录</title>

<meta name="renderer" content="webkit|ie-comp|ie-stand">

<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">

<meta http-equiv="Cache-Control" content="no-siteapp" />

<link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />

<link rel="stylesheet" href="/Public/xadmin/css/font.css">

<link rel="stylesheet" href="/Public/xadmin/css/xadmin.css">

<script type="text/javascript" src="/Public/xadmin/js/jquery.min.js"></script>

<script src="/Public/xadmin/lib/layui/layui.js" charset="utf-8"></script>

<script type="text/javascript" src="/Public/xadmin/js/xadmin.js"></script>

<link rel="stylesheet" href="/Public/css/back2.css">

<link rel="stylesheet" href="/Public/font-awesome/css/font-awesome.min.css">


<script charset="utf-8" src="/Public/js/jquery.form.js"></script>
<link rel="stylesheet" href="/Public/kinder/themes/default/default.css" />
<link rel="stylesheet" href="/Public/kinder/plugins/code/prettify.css" />
<script charset="utf-8" src="/Public/kinder/kindeditor-min.js"></script>
<script charset="utf-8" src="/Public/kinder/lang/zh_CN.js"></script>
<script charset="utf-8" src="/Public/kinder/plugins/code/prettify.js"></script>
<script type="text/javascript" src="/Public/js/My97DatePicker/WdatePicker.js"></script>
<script>
	KindEditor.ready(function(K) {
		var editor1 = K.create('textarea[class="htmltext"]', {
			cssPath : '/Public/kinder/plugins/code/prettify.css',
			uploadJson : '/Public/kinder/upload_json.php',
			fileManagerJson : '/Public/kinder/file_manager_json.php',
			allowFileManager : true,
			urlType:'domain',
			afterBlur: function () { this.sync(); },//注意这里（异步提交时需要同步）
			afterCreate: function () {
				var self = this;
				self.sync();//把富文本编辑器的内容放到 文本域里面去。
			}
		});
		prettyPrint();
	});
$(function(){
	//上传
		$('#uploadForm').ajaxForm({
			dataType: 'json',
				success: function(data){
						if(data.up_msg=='OK'){
							data.up_msg = '上传成功！';
							$('#img_wrap').html('<img src="/Attached/'+data.img_url+'" style="width:80px;height:80px;" />');
							$('#thumbs').val(data.img_url);
						}
						$('.tips').text(data.up_msg);
				},
				beforeSubmit:function(){
				}
			});
			$('.go_up').click(function(){
					$('#thumbs_hide').click();
			});
			$('#thumbs_hide').change(function(){
				$('#uploadForm').submit();
			});
			//上传2
		$('#uploadForm2').ajaxForm({
			dataType: 'json',
				success: function(data){
						if(data.up_msg=='OK'){
							data.up_msg = '上传成功！';
							$('#img_wrap2').html('<img src="/Attached/'+data.img_url+'" style="width:80px;height:80px;" />');
							$('#thumbs2').val(data.img_url);
						}
						$('.tips2').text(data.up_msg);
				},
				beforeSubmit:function(){
				}
			});
			$('.go_up2').click(function(){
					$('#thumbs_hide2').click();
			});
			$('#thumbs_hide2').change(function(){
				$('#uploadForm2').submit();
			});
		//   //验证
		// $("#fm1").validate({
		// 	rules: {
		// 		title: "required"
		// 	  },
		// 	   errorPlacement: function(error, element) {
		// 	 		error.addClass('alert_tips');
		// 			element.after('<span class="pos_rela alert_rela ver_mid"></span>');
		// 			element.siblings('.alert_rela').append(error);
		// 			error.width(error.html().length*12);
		// 	}
		//   });
});
</script>
</head>
	<div class="x-body">
			<form  class="layui-form" method="post" id="fm1">
				<input type="hidden" name="id" value="<?php echo ($res['id']); ?>" />
				<input type="hidden" name="type" value="<?php echo ($_GET['type']); ?>" />
				<input type="hidden" name="cat_id" value="<?php echo ($_GET['cid']); ?>" />
				<table class="tbl3 bg_f w_100 rad_10 mt_10 clr_79">
					<tr><td  style="width:100px;">基差</td><td><input name="jicha"  class="layui-input" value="<?php echo ($res["jicha"]); ?>" /></td></tr>
					<tr><td  style="width:100px;">点价合约</td><td><input name="dianjiaheyue" class="layui-input" value="<?php echo ($res["dianjiaheyue"]); ?>" /></td></tr>
					<tr><td  style="width:100px;">包数</td><td><input name="baoshu" class="layui-input" value="<?php echo ($res["baoshu"]); ?>" /></td></tr>
					<tr><td  style="width:100px;">基差</td><td><input name="jicha2" class="layui-input" value="<?php echo ($res["jicha2"]); ?>" /></td></tr>
					<tr><td  style="width:100px;">公重</td><td><input name="gongzhong" class="layui-input"  value="<?php echo ($res["gongzhong"]); ?>" /></td></tr>
					<tr><td  style="width:100px;">毛重</td><td><input name="maozhaong"  class="layui-input" value="<?php echo ($res["maozhaong"]); ?>" /></td></tr>
<!-- 					<tr><td >公司LOGO</td><td>
						<span id="img_wrap">
							<?php if($res["thumbs"] != ''): ?><img src="/Attached/<?php echo ($res["thumbs"]); ?>" style="width:80px;height:80px;" /><?php endif; ?>
						</span>
						<span>
							<input name="thumbs" id="thumbs" type="hidden"  value="<?php echo ($res["thumbs"]); ?>" />
							<input type="button" class="go_up layui-btn" value="上传"  />
							<span class="tips">2M以内、jpg/png </span>
						</span>
					</td></tr>
					<tr><td >名片二维码</td><td>
							<span id="img_wrap2">
								<?php if($res["thumbs2"] != ''): ?><img src="/Attached/<?php echo ($res["thumbs2"]); ?>" style="width:80px;height:80px;" /><?php endif; ?>
							</span>
							<span>
								<input name="thumbs2" id="thumbs2" type="hidden"  value="<?php echo ($res["thumbs2"]); ?>" />
								<input type="button" class="go_up2 layui-btn" value="上传"  />
								<span class="tips2">2M以内、jpg/png </span>
							</span>
					</td></tr> -->
					<!-- <tr ><td>内容</td><td><textarea class="htmltext w_100" name="content" style="height:500px;visibility:hidden;" ><?php echo ($res["content"]); ?></textarea></td></tr> -->
					<tr><td></td><td><input type="submit" value="保存" lay-submit lay-filter="formDemo" class="layui-btn" /></td></tr>
				</table>
			</form>
			<form style="display: none;"  id="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('Tools/runUpload');?>">
				<input type="file" name="thumbs_hide" id="thumbs_hide" />
			</form>
			<form style="display: none;"  id="uploadForm2" enctype="multipart/form-data"  method="post" action="<?php echo U('Tools/runUpload');?>">
				<input type="file" name="thumbs_hide" id="thumbs_hide2" />
			</form>
</div>
<script>
layui.use('laydate', function(){
	var laydate = layui.laydate;
	//执行一个laydate实例
	laydate.render({
		elem: '#summary' //指定元素
	});
});
	layui.use('form', function(){
	var form = layui.form;
	//监听提交
	form.on('submit(formDemo)', function(data){
		// $.post("<?php echo U('runEdit');?>",data.field, function(d){
			$.post("<?php echo U('runEdit');?>",$('#fm1').serialize(), function(d){
			if(d.code == 1){
				layer.alert("保存成功", {icon: 6},function () {
					window.parent.location.reload();
						var index = parent.layer.getFrameIndex(window.name);
									//关闭当前frame
									parent.layer.close(index);
						});
			}
		},'json');
			return false;
	});
});
</script>
</body>
</html>
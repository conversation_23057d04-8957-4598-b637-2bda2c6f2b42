<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">

<title>后台登录</title>

<meta name="renderer" content="webkit|ie-comp|ie-stand">

<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">

<meta http-equiv="Cache-Control" content="no-siteapp" />

<link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />

<link rel="stylesheet" href="/Public/xadmin/css/font.css">

<link rel="stylesheet" href="/Public/xadmin/css/xadmin.css">

<script type="text/javascript" src="/Public/xadmin/js/jquery.min.js"></script>

<script src="/Public/xadmin/lib/layui/layui.js" charset="utf-8"></script>

<script type="text/javascript" src="/Public/xadmin/js/xadmin.js"></script>

<link rel="stylesheet" href="/Public/css/back2.css">

<link rel="stylesheet" href="/Public/font-awesome/css/font-awesome.min.css">


</head>
<body>
<div class="x-body">
    <div class="layui-card">
        <div class="layui-card-header">
            <h4>添加规则详情 - <?php echo ($rule["rule_name"]); ?></h4>
        </div>
    </div>

    <form class="layui-form" id="form1">
        <input type="hidden" name="rule_id" value="<?php echo ($rule["id"]); ?>">

        <div class="layui-form-item">
            <label for="field_id" class="layui-form-label">
                <span class="x-red">*</span>条件字段
            </label>
            <div class="layui-input-inline">
                <select name="field_id" lay-verify="required" lay-filter="field_id">
                    <option value="">请选择条件字段</option>
                    <?php if(is_array($condition_fields)): foreach($condition_fields as $key=>$v): ?><option value="<?php echo ($v["id"]); ?>" data-type='<?php echo ($v["field_type"]); ?>' data-range='<?php echo ($v["is_range"]); ?>' data-options='<?php echo ($v["select_options"]); ?>'><?php echo ($v["field_name"]); if($v['field_unit']): ?>(<?php echo ($v["field_unit"]); ?>)<?php endif; ?></option><?php endforeach; endif; ?>
                </select>
            </div>
            <div class="layui-form-mid layui-word-aux">
                选择要设置条件的字段
            </div>
        </div>

        <div class="layui-form-item">
            <label for="condition_type" class="layui-form-label">
                <span class="x-red">*</span>条件类型
            </label>
            <div class="layui-input-inline">
                <select name="condition_type" lay-verify="required" lay-filter="condition_type">
                    <option value="">请选择条件类型</option>
                    <?php if(is_array($condition_types)): foreach($condition_types as $k=>$v): ?><option value="<?php echo ($k); ?>"><?php echo ($v); ?></option><?php endforeach; endif; ?>
                </select>
            </div>
            <div class="layui-form-mid layui-word-aux">
                单值条件或范围条件
            </div>
        </div>

        <div class="layui-form-item">
            <label for="operator" class="layui-form-label">
                <span class="x-red">*</span>操作符
            </label>
            <div class="layui-input-inline">
                <select name="operator" lay-verify="required">
                    <option value="">请选择操作符</option>
                    <?php if(is_array($operators)): foreach($operators as $k=>$v): endforeach; endif; ?>
                </select>
            </div>
            <div class="layui-form-mid layui-word-aux">
                选择比较操作符
            </div>
        </div>

        <div class="layui-form-item" id="single_value_div">
            <label for="condition_value" class="layui-form-label">
                <span class="x-red">*</span>条件值
            </label>
            <div id="condition_value_input" class="layui-input-inline">
                <input type="text" name="condition_value"
                       autocomplete="off" class="layui-input" placeholder="请输入条件值">
            </div>
            <div id="condition_value_select" class="layui-input-inline" style="display:none;">
                <select name="condition_value_select" style="display:none;">
                    <option value="">请选择条件值</option>
                </select>
            </div>
            <div class="layui-form-mid layui-word-aux">
                <span class="x-red">*</span>用于比较的值
            </div>
        </div>

        <div class="layui-form-item" id="range_value_div" style="display:none;">
            <label class="layui-form-label">
                <span class="x-red">*</span>范围值
            </label>
            <div class="layui-input-inline" style="width: 100px;">
                <input type="text" name="min_value" placeholder="最小值" class="layui-input">
            </div>
            <div class="layui-form-mid">~</div>
            <div class="layui-input-inline" style="width: 100px;">
                <input type="text" name="max_value" placeholder="最大值" class="layui-input">
            </div>
            <div class="layui-form-mid layui-word-aux">
                <span class="x-red">*</span>设置范围的最小值和最大值
            </div>
        </div>

        <div class="layui-form-item">
            <label for="discount_value" class="layui-form-label">
                <span class="x-red">*</span>升贴水值
            </label>
            <div class="layui-input-inline">
                <input type="number" step="0.01" id="discount_value" name="discount_value" required=""
                       lay-verify="required"
                       autocomplete="off" class="layui-input" placeholder="请输入升贴水值">
            </div>
            <div class="layui-form-mid layui-word-aux">
                <span class="x-red">*</span>正数为升水，负数为贴水
            </div>
        </div>

        <div class="layui-form-item">
            <label for="" class="layui-form-label"></label>
            <button class="layui-btn" lay-filter="add" lay-submit="">增加</button>
        </div>
    </form>
</div>

<script>
    layui.use(['form', 'layer'], function () {
        $ = layui.jquery;
        var form = layui.form,
            layer = layui.layer;

        // 根据字段类型更新操作符选项
        function updateOperatorOptions(fieldType) {
            var $operator = $('select[name="operator"]');
            var currentValue = $operator.val();
            $operator.empty();
            $operator.append('<option value="">请选择操作符</option>');

            if (fieldType == 'select') {
                // 选择型字段只支持等于和不等于
                $operator.append('<option value="=">等于</option>');
                $operator.append('<option value="!=">不等于</option>');
                $operator.append('<option value="in">包含于</option>');
            } else if (fieldType == 'number') {
                // 数值型字段支持所有比较操作符
                $operator.append('<option value=">">大于</option>');
                $operator.append('<option value="<">小于</option>');
                $operator.append('<option value="=">等于</option>');
                $operator.append('<option value=">=">大于等于</option>');
                $operator.append('<option value="<=">小于等于</option>');
                $operator.append('<option value="!=">不等于</option>');
                $operator.append('<option value="between">介于</option>');
            } else {
                // 字符串型字段
                $operator.append('<option value="=">等于</option>');
                $operator.append('<option value="!=">不等于</option>');
            }

            // 保持当前选择
            if (currentValue) {
                $operator.val(currentValue);
            }

            form.render('select');
        }

        //监听字段选择
        form.on('select(field_id)', function (data) {
            var $option = $(data.elem).find('option:selected');
            var fieldType = $option.data('type');
            var isRange = $option.data('range');
            var selectOptions = $option.data('options');

            // 根据字段类型和是否支持范围来显示条件类型选项
            var $conditionType = $('select[name="condition_type"]');
            $conditionType.empty();
            $conditionType.append('<option value="">请选择条件类型</option>');
            $conditionType.append('<option value="single">单值条件</option>');

            if (isRange == 1) {
                $conditionType.append('<option value="range">范围条件</option>');
            }

            // 根据字段类型更新操作符选项
            updateOperatorOptions(fieldType);

            // 根据字段类型显示不同的输入控件
            if (fieldType == 'select' && selectOptions) {
                // 选择型字段显示下拉选择
                $('#condition_value_input').hide();
                $('#condition_value_select').show();

                var $select = $('#condition_value_select select');
                $select.empty();
                $select.append('<option value="">请选择条件值</option>');

                try {
                    var options = JSON.parse(JSON.stringify(selectOptions));
                    if (Array.isArray(options)) {
                        options.forEach(function (option) {
                            $select.append('<option value="' + option + '">' + option + '</option>');
                        });
                    }
                } catch (e) {
                    console.error('解析选择项失败:', e);
                }
            } else {
                // 其他类型显示文本输入
                $('#condition_value_input').show();
                $('#condition_value_select').hide();
            }

            form.render('select');
        });

        //监听条件类型选择
        form.on('select(condition_type)', function (data) {
            if (data.value == 'range') {
                $('#single_value_div').hide();
                $('#range_value_div').show();
            } else {
                $('#single_value_div').show();
                $('#range_value_div').hide();
            }
        });

        // 页面加载时初始化
        $(document).ready(function () {
            // 默认显示单值条件输入
            $('#condition_value_input').show();
            $('#condition_value_select').hide();
        });

        //自定义验证规则
        form.verify({
            condition_value: function (value) {
                var conditionType = $('select[name="condition_type"]').val();
                if (conditionType == 'single') {
                    // 检查当前显示的是输入框还是选择框
                    var actualValue = '';
                    if ($('#condition_value_select').is(':visible')) {
                        actualValue = $('select[name="condition_value_select"]').val();
                    } else {
                        actualValue = $('input[name="condition_value"]').val();
                    }

                    if (!actualValue || actualValue.length < 1) {
                        return '条件值不能为空';
                    }
                }
            },
            discount_value: function (value) {
                if (isNaN(value)) {
                    return '升贴水值必须是数字';
                }
            }
        });

        //监听提交
        form.on('submit(add)', function (data) {
            console.log('原始表单数据:', data);

            // 处理condition_value字段
            var formData = data.field;

            // 如果显示的是选择框，则使用选择框的值
            if ($('#condition_value_select').is(':visible')) {
                var selectValue = $('select[name="condition_value_select"]').val();
                formData.condition_value = selectValue;
                console.log('使用选择框的值:', selectValue);
            } else {
                // 使用文本输入框的值
                var inputValue = $('input[name="condition_value"]').val();
                formData.condition_value = inputValue;
                console.log('使用输入框的值:', inputValue);
            }

            // 删除多余的字段
            delete formData.condition_value_select;

            console.log('处理后的表单数据:', formData);

            //发异步，把数据提交给php
            $.ajax({
                url: "<?php echo U('runAddDetail');?>",
                type: 'POST',
                data: formData,
                dataType: 'json',
                success: function (res) {
                    if (res.code == 1) {
                        layer.alert("增加成功", {icon: 1}, function () {
                            // 获得frame索引
                            var index = parent.layer.getFrameIndex(window.name);
                            //关闭当前frame
                            parent.layer.close(index);
                            // 刷新父页面
                            parent.location.reload();
                        });
                    } else {
                        layer.alert(res.msg, {icon: 2});
                    }
                },
                error: function () {
                    layer.alert("请求失败", {icon: 2});
                }
            });
            return false;
        });
    });
</script>
</body>
</html>
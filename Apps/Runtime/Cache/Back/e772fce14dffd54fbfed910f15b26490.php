<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>

<html lang="zh-CN">

<head>

<meta charset="UTF-8">

<title>后台登录</title>

<meta name="renderer" content="webkit|ie-comp|ie-stand">

<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">

<meta http-equiv="Cache-Control" content="no-siteapp" />

<link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />

<link rel="stylesheet" href="/Public/xadmin/css/font.css">

<link rel="stylesheet" href="/Public/xadmin/css/xadmin.css">

<script type="text/javascript" src="/Public/xadmin/js/jquery.min.js"></script>

<script src="/Public/xadmin/lib/layui/layui.js" charset="utf-8"></script>

<script type="text/javascript" src="/Public/xadmin/js/xadmin.js"></script>

<link rel="stylesheet" href="/Public/css/back2.css">

<link rel="stylesheet" href="/Public/font-awesome/css/font-awesome.min.css">



<script charset="utf-8" src="/Public/js/jquery.form.js"></script>

<link rel="stylesheet" href="/Public/kinder/themes/default/default.css" />

<link rel="stylesheet" href="/Public/kinder/plugins/code/prettify.css" />

<script charset="utf-8" src="/Public/kinder/kindeditor-min.js"></script>

<script charset="utf-8" src="/Public/kinder/lang/zh_CN.js"></script>

<script charset="utf-8" src="/Public/kinder/plugins/code/prettify.js"></script>

<script>

	KindEditor.ready(function(K) {

		var editor1 = K.create('textarea[name="content"]', {

			cssPath : '/Public/kinder/plugins/code/prettify.css',

			uploadJson : '/Public/kinder/upload_json.php',

			fileManagerJson : '/Public/kinder/file_manager_json.php',

			allowFileManager : true,

			afterCreate : function() {

				var self = this;



			}

		});

		prettyPrint();

	});

	</script>

</head>

<body>

		<div class="x-body">

			<form  class="layui-form" method="post">

				<table class="layui-table">

					<tr><td width="100">名称：</td><td><input name="name" value="<?php echo ($res["name"]); ?>"  class="layui-input" lay-verify="required" /> </td></tr>

					<!-- <tr><td width="100">名称en：</td><td><input name="name_en" value="<?php echo ($res["name_en"]); ?>"  class="layui-input" lay-verify="required" /> </td></tr> -->

					<tr style="display: none;"><td>是否内容页：</td><td>

						<div  id="let_show">

						<label>

							<input name="type" type="radio" value="1" <?php if($res["type"] == 1): ?>checked="true"<?php endif; ?>  /> <span class="ver_mid">是</span>

						</label>

						&nbsp;&nbsp;

						<label>

							<input name="type" type="radio" value="0" <?php if($res["type"] == 0): ?>checked="true"<?php endif; ?>  /> <span class="ver_mid">否</span>

						</label>

						</div>

					</td></tr>

					<?php if($res["type"] != 0): ?><tr id="show_cat_content">

							<td>栏目内容：</td>

							<td>

								<textarea name="content" style="width:800px;height:500px;visibility:hidden;" ><?php echo ($res["content"]); ?></textarea>	

							</td>

						</tr><?php endif; ?> 

					<tr><td></td><td><input type="submit" value="保存" lay-submit lay-filter="formDemo" class="layui-btn" /></td></tr>

				</table>

				<input name="id" value="<?php echo ($res["id"]); ?>" type="hidden" />

			</form>

		</div>

	<script>

		$(function() {

			$('#let_show').click(function(){

				if($(this).find('input').prop('checked')){

						$('#show_cat_content').show();

				}else{

					$('#show_cat_content').hide();	

				}

			});

			

			  layui.use('form', function(){

			  var form = layui.form;

			  

			  //监听提交

			  form.on('submit(formDemo)', function(data){

			    $.post("<?php echo U('runEdit');?>",data.field, function(d){

			    	if(d.code == 1){

			    		layer.alert("保存成功", {icon: 6},function () {

			    			window.parent.location.reload();

			    				var index = parent.layer.getFrameIndex(window.name);

				                //关闭当前frame

				                parent.layer.close(index);



			            });

			    	}

			    },'json');



			      return false;

			  });





			});

		});



	</script>



</body>

</html>
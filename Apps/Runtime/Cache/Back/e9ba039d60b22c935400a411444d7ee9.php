<?php if (!defined('THINK_PATH')) exit();?><!doctype html>
<html lang="en">
<head>
	<meta charset="UTF-8">

<title>后台登录</title>

<meta name="renderer" content="webkit|ie-comp|ie-stand">

<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">

<meta http-equiv="Cache-Control" content="no-siteapp" />

<link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />

<link rel="stylesheet" href="/Public/xadmin/css/font.css">

<link rel="stylesheet" href="/Public/xadmin/css/xadmin.css">

<script type="text/javascript" src="/Public/xadmin/js/jquery.min.js"></script>

<script src="/Public/xadmin/lib/layui/layui.js" charset="utf-8"></script>

<script type="text/javascript" src="/Public/xadmin/js/xadmin.js"></script>

<link rel="stylesheet" href="/Public/css/back2.css">

<link rel="stylesheet" href="/Public/font-awesome/css/font-awesome.min.css">


</head>
<body>
	<!-- 顶部开始 -->
	<div class="container" style="background: #fff;">
		<div  style="background: #fff;z-index: 999;position: relative;">
			<div class="logo"><a href="javascript:;"  style="color:#333;">后台管理 <span class="clr_ora f_24" >系统</span></a></div>
			<div class="left_open" >
				<i  title="展开左侧栏"  class="fa fa-bars" style="color:#333;font-size: 24px;" aria-hidden="true"></i>
			</div>
			<ul class="layui-nav right" lay-filter="" style="z-index: 99;">
	<!--                   <li class="layui-nav-item to-index"><a href="<?php echo U('Home/Index/index');?>" target="_blank" style="color:#333;">
	<i class="fa fa-home"  aria-hidden="true" style="font-size: 16px;"></i>  前台首页</a></li> -->
	<li class="layui-nav-item to-index"><a href="<?php echo U('Index/exits');?>"  style="color:#333;" >
		<i class="fa fa-reply-all" aria-hidden="true" style="font-size: 14px;"></i> 退出系统</a></li>
	</ul>
</div>
<div class="left-nav">
	<div id="side-nav">
		<ul id="nav">
			<?php if(is_array($leftNav)): foreach($leftNav as $key=>$v): ?><li style="border-bottom:0;padding:0 10px;">
					<?php $a = 'cat1_'.$v['id']; if($session_admin['type'] == 0){ ?>
					<a href="javascript:;" style="color:#aeb2b7;">
						<?php echo ($v["icon"]); ?>
						<cite><?php echo ($v["name"]); ?></cite>
						<i class="iconfont nav_right">&#xe697;</i>
					</a>
					<ul class="sub-menu">
						<?php if(is_array($v["child"])): foreach($v["child"] as $key=>$v2): ?><li style="border-bottom:0;background: #35404d;">
								<a _href="<?php echo ($v2["url"]); ?>" style="color:#aeb2b7;">
									<?php if($v2['icon'] != ''): echo ($v2["icon"]); ?>
										<?php else: ?>
										<i class="fa fa-chevron-right" aria-hidden="true"></i><?php endif; ?>
									<cite><?php echo ($v2["name"]); ?></cite>
								</a>
							</li><?php endforeach; endif; ?>
					</ul>
					<?php }else{ if($session_admin['permission'][$a]!=''){ ?>
					<a href="javascript:;" style="color:#aeb2b7;">
						<?php echo ($v["icon"]); ?>
						<cite><?php echo ($v["name"]); ?></cite>
						<i class="iconfont nav_right">&#xe697;</i>
					</a>
					<ul class="sub-menu">
						<?php if(is_array($v["child"])): foreach($v["child"] as $key=>$v2): $cat2 = explode(',', $session_admin['permission'][$a]); if(in_array($v2['id'], $cat2)){ ?>
							<li style="border-bottom:0;background: #35404d;">
								<a _href="<?php echo ($v2["url"]); ?>"  style="color:#aeb2b7;">
									<?php if($v2['icon'] != ''): echo ($v2["icon"]); ?>
										<?php else: ?>
										<i class="fa fa-chevron-right" aria-hidden="true"></i><?php endif; ?>
									<cite><?php echo ($v2["name"]); ?></cite>
								</a>
							</li>
							<?php } endforeach; endif; ?>
				</ul>
				<?php } } ?>
	</li><?php endforeach; endif; ?>
</ul>
</div>
</div>
<script type="text/javascript">
				// $('.sub-menu').show();
			</script>
			<div class="page-content"   style="bottom:0;top:10px;">
				<div class="layui-tab-content" >
					<div class="layui-tab-item layui-show">
						<iframe src="<?php echo U('Main/welcome');?>" frameborder="0" scrolling="yes" class="x-iframe" name="myframe" id="myframe" ></iframe>
					</div>
				</div>
			</div>
			<script>
				$('.sub-menu li').click(function(){
					var a = $(this).find('a').attr('_href');
					$(this).find('a').css('color','#ff6c60');
					$(this).siblings().find('a').css('color','#aeb2b7');
					$('#myframe').attr('src',a);
				});
			</script>
			<div class="page-content-bg" ></div>
		</div>
	</body>
	</html>
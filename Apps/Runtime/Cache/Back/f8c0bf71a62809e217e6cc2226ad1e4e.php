<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>
<html lang="zh-CN">
<head>
	<meta charset="UTF-8">

<title>后台登录</title>

<meta name="renderer" content="webkit|ie-comp|ie-stand">

<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">

<meta http-equiv="Cache-Control" content="no-siteapp" />

<link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />

<link rel="stylesheet" href="/Public/xadmin/css/font.css">

<link rel="stylesheet" href="/Public/xadmin/css/xadmin.css">

<script type="text/javascript" src="/Public/xadmin/js/jquery.min.js"></script>

<script src="/Public/xadmin/lib/layui/layui.js" charset="utf-8"></script>

<script type="text/javascript" src="/Public/xadmin/js/xadmin.js"></script>

<link rel="stylesheet" href="/Public/css/back2.css">

<link rel="stylesheet" href="/Public/font-awesome/css/font-awesome.min.css">


	<script charset="utf-8" src="/Public/js/jquery.form.js"></script>
	<script type="text/javascript" src="/Public/js/ion.rangeSlider.min.js"></script>
	<link rel="stylesheet" type="text/css" href="/Public/css/ion.rangeSlider.min.css">
	<style>
		.tbl2 td,.tbl2 th{padding:0 5px;text-align: left;}
		.ellipsis1{overflow: hidden;
text-overflow: ellipsis;
display: -webkit-box;
-webkit-line-clamp: 1;
-webkit-box-orient: vertical;}
	</style>
</head>
<div  class="x-body">

		<div class="border1 pad_20 bg_f mt_10">
			<ul class="mian_ul clearfix al_ct f_18">
				<?php if(is_array($chandi)): foreach($chandi as $k=>$v): if($def == $v['id']): ?><li><a href="<?php echo U('index',['def'=>$v['id']]);?>" class="clr_3 mian_cur"><?php echo ($v["name"]); ?></a></li>
					<?php else: ?>
						<li><a href="<?php echo U('index',['def'=>$v['id']]);?>" class="clr_3 mian_norm"><?php echo ($v["name"]); ?></a></li><?php endif; endforeach; endif; ?>
			</ul>

			<ul class="chandi_ul f_14 mt_20 fl" style="line-height:30px;margin-left:5px;width:370px;">
				<li  style="display: inline-block;" class="ver_top">产　地：</li>
				<li  style="display: inline-block;"  >
					<div id="chandi_click">
						<?php if(is_array($chandi_default)): foreach($chandi_default as $key=>$v): if(strpos($_GET['cd'], '|'.$v['id'].'-|') !== false): ?><a href="#." class="clr_3 mr_10 xuan_cur" myid="<?php echo ($v["id"]); ?>"><?php echo ($v["name"]); ?></a>
							<?php else: ?>
								<a href="#." class="clr_3 mr_10 " myid="<?php echo ($v["id"]); ?>"><?php echo ($v["name"]); ?></a><?php endif; endforeach; endif; ?>
					</div>
				</li>
			</ul>

			<ul class="chandi_ul f_14 mt_20 fl "  id="leixing_ul" style="line-height:30px; margin-left:5px;width:375px;">
				<li>类　型：</li>
				<?php if(is_array($xls_leixing)): foreach($xls_leixing as $key=>$v): if(strpos($_GET['leixing'], '|'.$v.'|') !== false): ?><li><a href="#." class="clr_3 xuan_cur"><?php echo ($v); ?></a></li>
					<?php else: ?>
						<li><a href="#." class="clr_3"><?php echo ($v); ?></a></li><?php endif; endforeach; endif; ?>
			</ul>

			<ul class="chandi_ul f_14 mt_20 fl " id="niandu_ul" style="line-height:30px; margin-left:5px; width:330px;">
				<li>年　度：</li>
				<?php if(is_array($xls_niandu)): foreach($xls_niandu as $key=>$v): if(strpos($_GET['pihao_kunkao'], '|'.$v.'|') !== false): ?><li><a href="#." class="clr_3 xuan_cur"><?php echo ($v); ?></a></li>
					<?php else: ?>
						<li><a href="#." class="clr_3"><?php echo ($v); ?></a></li><?php endif; endforeach; endif; ?>
			</ul>

			<div class="cls"></div>
			<div id="child_wraper">
				<?php if(is_array($chandi_default)): foreach($chandi_default as $key=>$v): ?><span id="child_<?php echo ($v["id"]); ?>">
						<?php if($_GET['cd2'] != '' and $_GET['cd2'] != '|'): if(is_array($cd2[$v['id']])): foreach($cd2[$v['id']] as $key=>$v2): if(strpos($_GET['cd2'], '|'.$v['id'].'-'.$v2['id'].'-|') !== false): ?><a href="#." class="clr_3 mr_10 xuan_cur f_14" pid="<?php echo ($v["id"]); ?>" myid="<?php echo ($v2["id"]); ?>"><?php echo ($v2["name"]); ?></a>
								<?php else: ?>
									<a href="#." class="clr_3 mr_10 f_14" pid="<?php echo ($v["id"]); ?>" myid="<?php echo ($v2["id"]); ?>"><?php echo ($v2["name"]); ?></a><?php endif; endforeach; endif; endif; ?>
					</span><?php endforeach; endif; ?>
			</div>

			<div id="grandson_wraper" class="mt_10">
				<?php if(is_array($xx)): foreach($xx as $k=>$v): if(is_array($v)): foreach($v as $key=>$v2): ?><span id="grandson_<?php echo ($v2["id"]); ?>" gpid="<?php echo ($k); ?>" pid="<?php echo ($v2["id"]); ?>">
							<?php if($_GET['cd3'] != '' and $_GET['cd3'] != '|' and strpos($_GET['cd2'], '|'.$k.'-'.$v2['id'].'-|') !== false): if(is_array($v2["children"])): foreach($v2["children"] as $key=>$v3): if(strpos($_GET['cd3'], '|'.$k.'-'.$v3['pid'].'-'.$v3['id'].'-|') !== false): ?><a href="#." gpid="<?php echo ($k); ?>" pid="<?php echo ($v3["pid"]); ?>" myid="<?php echo ($v3["id"]); ?>" class="mr_10 clr_3 f_14 xuan_cur"><?php echo ($v3["name"]); ?></a>
									<?php else: ?>
										<a href="#." gpid="<?php echo ($k); ?>" pid="<?php echo ($v3["pid"]); ?>" myid="<?php echo ($v3["id"]); ?>" class="mr_10 clr_3 f_14"><?php echo ($v3["name"]); ?></a><?php endif; endforeach; endif; endif; ?>
						</span><?php endforeach; endif; endforeach; endif; ?>
			</div>




			<script type="text/javascript">

				//点击子产地
				$('#child_wraper').on('click', 'a', function(){
					var that  = $(this);

					if(!that.hasClass('xuan_cur')){
						that.addClass('xuan_cur');
					}else{
						that.removeClass('xuan_cur');
					}
				});

				//点击父产地
				$('#chandi_click').on('click', 'a', function(){
					var that  = $(this);
					if(!that.hasClass('xuan_cur')){
						$.getJSON('<?php echo U("Tools/getCat");?>', {id:that.attr('myid')}, function(d){
							// console.log(d)
							$.each(d, function(k,v){
								$('#child_'+that.attr('myid')).append('<a href="#." pid="'+v.pid+'" myid="'+v.id+'" class="mr_10 clr_3 f_14">'+v.name+'</a>');
								$('#grandson_wraper').append('<span id="grandson_'+v.id+'" gpid="'+v.pid+'"></span>')
							});
							that.addClass('xuan_cur');
						});
					}else{
						that.removeClass('xuan_cur');
					}

					$('#child_'+that.attr('myid')+' a[pid="'+that.attr('myid')+'"]').remove();
					$('#grandson_wraper a[gpid="'+that.attr('myid')+'"]').remove();
					//删除对应二级的占位
					$('#grandson_wraper span[gpid='+that.attr('myid')+']').remove();
				});
			</script>


			<script type="text/javascript">
				var leixing_str= '<?php echo ($_GET["leixing"]); ?>' == '' ? '|' : '<?php echo ($_GET["leixing"]); ?>' ;
				// console.log(leixing_str)
				$('#leixing_ul li a').click(function(){
					if(!$(this).hasClass('xuan_cur')){
						$(this).addClass('xuan_cur');
						if(leixing_str.indexOf('|'+$(this).text()+'|') ==-1){
							leixing_str += $(this).text()+'|';
						}
					}else{
						$(this).removeClass('xuan_cur');
						leixing_str = leixing_str.replace('|'+$(this).text()+'|', '|');
					}

					$('input[name="leixing"]').val(leixing_str);
				});
			</script>


			<script type="text/javascript">
				var niandu_str=  '<?php echo ($_GET["pihao_kunkao"]); ?>' == '' ? '|' : '<?php echo ($_GET["pihao_kunkao"]); ?>' ;
				$('#niandu_ul li a').click(function(){
					if(!$(this).hasClass('xuan_cur')){
						$(this).addClass('xuan_cur');
						if(niandu_str.indexOf('|'+$(this).text()+'|') ==-1){
							niandu_str += $(this).text()+'|';
						}
					}else{
						$(this).removeClass('xuan_cur');
						niandu_str = niandu_str.replace('|'+$(this).text()+'|', '|');
					}

					 $('input[name="pihao_kunkao"]').val(niandu_str);
				});
			</script>

			<div class="cls"></div>

			<ul class="chandi_ul f_14  fl" style="width:380px;">
				<li class="length">长　度</li>
				<li style="width:240px;"><input type="text" class="changdu_range" name="my_range" value=""   /></li>
			</ul>
			<script>
				$(".changdu_range").ionRangeSlider({
					type: "double",
					skin:"round",
						type: "double",
						min: 25,
						max: 32,
						from: '<?php echo ($_GET["changdu_from"]); ?>' == '' ? 25 : '<?php echo ($_GET["changdu_from"]); ?>',
						to: '<?php echo ($_GET["changdu_to"]); ?>' == '' ? 32 : '<?php echo ($_GET["changdu_to"]); ?>',
						step:0.1, 
						onFinish: function (data) { //拖动结束回调
							 // console.log(data)
							 $('input[name="changdu_from"]').val(data.from);
							 $('input[name="changdu_to"]').val(data.to);
						}
		 
				});
			</script>


			
			<ul class="chandi_ul f_14 fl" style="width:380px;">
				<li  class="length">强　力</li>
				<li style="width:240px;"><input type="text" class="qiangli_range" name="my_range" value=""   /></li>
			</ul>
			<script>
				$(".qiangli_range").ionRangeSlider({
					type: "double",
					skin:"round",
						type: "double",
						min: 25,
						max: 32,
						from: '<?php echo ($_GET["qiangli_from"]); ?>' == '' ? 25 : '<?php echo ($_GET["qiangli_from"]); ?>',
						to: '<?php echo ($_GET["qiangli_to"]); ?>' == '' ? 32 : '<?php echo ($_GET["qiangli_to"]); ?>',
						step:0.1, 
						onFinish: function (data) { //拖动结束回调
							 // console.log(data)
								$('input[name="qiangli_from"]').val(data.from);
							 $('input[name="qiangli_to"]').val(data.to);
						}

		 
				});
			</script>

			<ul class="chandi_ul f_14  fl"  style="width:380px;">
				<li class="length">马　值</li>
				<li style="width:240px;"><input type="text" class="mazhi_range" name="my_range" value=""   /></li>
			</ul>
			<script>
				$(".mazhi_range").ionRangeSlider({
					type: "double",
					skin:"round",
						type: "double",
						min: 2.5,
						max: 5.5,
						from: '<?php echo ($_GET["mazhi_from"]); ?>' == '' ? 2.5 : '<?php echo ($_GET["mazhi_from"]); ?>',
						to: '<?php echo ($_GET["mazhi_to"]); ?>' == '' ? 5.5 : '<?php echo ($_GET["mazhi_to"]); ?>',
						step:0.1, 
						onFinish: function (data) { //拖动结束回调
							 // console.log(data)
							 $('input[name="mazhi_from"]').val(data.from);
							 $('input[name="mazhi_to"]').val(data.to);
						}
		 
				});
			</script>



			<div class="cls"></div>

			<ul class="chandi_ul f_14  fl" style="width:380px;">
				<li class="length">回　潮</li>
				<li style="width:240px;"><input type="text" class="huichao_range" name="my_range" value=""   /></li>
			</ul>
			<script>
				$(".huichao_range").ionRangeSlider({
					type: "double",
					skin:"round",
						type: "double",
						min: 0,
						max: 10,
						from: '<?php echo ($_GET["huichaolv_from"]); ?>' == '' ? 0 : '<?php echo ($_GET["huichaolv_from"]); ?>',
						to: '<?php echo ($_GET["huichaolv_to"]); ?>' == '' ? 10 : '<?php echo ($_GET["huichaolv_to"]); ?>',
						step:0.1, 
						onFinish: function (data) { //拖动结束回调
							 // console.log(data)
							 $('input[name="huichaolv_from"]').val(data.from);
							 $('input[name="huichaolv_to"]').val(data.to);
						}
		 
				});
			</script>

			<ul class="chandi_ul f_14  fl"  style="width:380px;">
				<li class="length">整齐度</li>
				<li style="width:240px;"><input type="text" class="zhengqidu_range" name="my_range" value=""   /></li>
			</ul>
			<script>
				$(".zhengqidu_range").ionRangeSlider({
					type: "double",
					skin:"round",
						type: "double",
						min: 77,
						max: 90,
						from: '<?php echo ($_GET["zhengqidu_from"]); ?>' == '' ? 77 : '<?php echo ($_GET["zhengqidu_from"]); ?>',
						to: '<?php echo ($_GET["zhengqidu_to"]); ?>' == '' ? 90 : '<?php echo ($_GET["zhengqidu_to"]); ?>',
						step:0.1, 
						onFinish: function (data) { //拖动结束回调
							 // console.log(data)
							 $('input[name="zhengqidu_from"]').val(data.from);
							 $('input[name="zhengqidu_to"]').val(data.to);
						}
		 
				});
			</script>

			<ul class="chandi_ul f_14  fl" style="width:380px;">
				<li class="length">含　杂</li>
				<li style="width:240px;"><input type="text" class="hanza_range" name="my_range" value=""   /></li>
			</ul>
			<script>
				$(".hanza_range").ionRangeSlider({
					type: "double",
					skin:"round",
						type: "double",
						min: 0,
						max: 6,
						from: '<?php echo ($_GET["hanzalv_from"]); ?>' == '' ? 0 : '<?php echo ($_GET["hanzalv_from"]); ?>',
						to: '<?php echo ($_GET["hanzalv_to"]); ?>' == '' ? 6 : '<?php echo ($_GET["hanzalv_to"]); ?>',
						step:0.1, 
						onFinish: function (data) { //拖动结束回调
							 // console.log(data)
								$('input[name="hanzalv_from"]').val(data.from);
							 $('input[name="hanzalv_to"]').val(data.to);
						}
		 
				});
			</script>


			<div class="cls"></div>


			<div class="bottom">
				<ul class="chandi_ul f_14  fl" style="width:380px;">
					<li class="green">白棉1/2/3级</li>
					<li style="width:240px;">
						<input type="text" class="bm123_range" name="bm123_range" value=""   />
					</li>
				</ul>
				<script>
					$(".bm123_range").ionRangeSlider({
						type: "double",
						skin:"round",
							type: "double",
							min: 0,
							max: 100,
							from: '<?php echo ($_GET["bm123_from"]); ?>' == '' ? 0 : '<?php echo ($_GET["bm123_from"]); ?>',
							to:'<?php echo ($_GET["bm123_to"]); ?>' == '' ? 100 : '<?php echo ($_GET["bm123_to"]); ?>',
							step:0.1, 
							onFinish: function (data) { //拖动结束回调
								 // console.log(data)
								 $('input[name="bm123_from"]').val(data.from);
								 $('input[name="bm123_to"]').val(data.to);
							}
			 
					});
				</script>
				<ul class="chandi_ul f_14  fl" style="width:380px;">
					<li class="green">白棉4/5级</li>
					<li style="width:240px;"><input type="text" class="bm45_range" name="bm45_range" value=""   /></li>
				</ul>
				<script>
					$(".bm45_range").ionRangeSlider({
						type: "double",
						skin:"round",
							type: "double",
							min: 0,
							max: 100,
							from: '<?php echo ($_GET["bm45_from"]); ?>' == '' ? 0 : '<?php echo ($_GET["bm45_from"]); ?>',
							to: '<?php echo ($_GET["bm45_to"]); ?>' == '' ? 100 : '<?php echo ($_GET["bm45_to"]); ?>',
							step:0.1, 
							onFinish: function (data) { //拖动结束回调
								 // console.log(data)
									$('input[name="bm45_from"]').val(data.from);
								 $('input[name="bm45_to"]').val(data.to);
							}

			 
					});
				</script>
				<ul class="chandi_ul f_14  fl"  style="width:380px;">
					<li class="green">淡点污1/2/3级</li>
					<li style="width:240px;">
						<input type="text" class="ddw123_range" name="ddw123_range" value=""   />
					</li>
				</ul>
				<script>
					$(".ddw123_range").ionRangeSlider({
						type: "double",
						skin:"round",
							type: "double",
							min: 0,
							max: 100,
							from: '<?php echo ($_GET["ddw123_from"]); ?>' == '' ? 0 : '<?php echo ($_GET["ddw123_from"]); ?>',
							to: '<?php echo ($_GET["ddw123_to"]); ?>' == '' ? 100 : '<?php echo ($_GET["ddw123_to"]); ?>',
							step:0.1, 
							onFinish: function (data) { //拖动结束回调
								 // console.log(data)
								 $('input[name="ddw123_from"]').val(data.from);
								 $('input[name="ddw123_to"]').val(data.to);
							}
			 
					});
				</script>

				<div class="cls"></div>

				<ul class="chandi_ul f_14  fl" style="width:380px;">
					<li class="green">淡黄染1/2/3级</li>
					<li style="width:240px;">
						<input type="text" class="dhr123_range" name="dhr123_range" value=""   />
					</li>
				</ul>
				<script>
					$(".dhr123_range").ionRangeSlider({
						type: "double",
						skin:"round",
							type: "double",
							min: 0,
							max: 100,
							from: '<?php echo ($_GET["dhr123_from"]); ?>' == '' ? 0 : '<?php echo ($_GET["dhr123_from"]); ?>',
							to: '<?php echo ($_GET["dhr123_to"]); ?>' == '' ? 100 : '<?php echo ($_GET["dhr123_to"]); ?>',
							step:0.1, 
							onFinish: function (data) { //拖动结束回调
								 // console.log(data)
								 $('input[name="dhr123_from"]').val(data.from);
								 $('input[name="dhr123_to"]').val(data.to);
							}
			 
					});
				</script>
				<ul class="chandi_ul f_14  fl" style="width:380px;">
					<li class="green">黄染1/2级</li>
					
					<li style="width:240px;"><input type="text" class="hr12_range" name="hr12_range" value=""   /></li>
				</ul>
				<script>
					$(".hr12_range").ionRangeSlider({
						type: "double",
						skin:"round",
							type: "double",
							min: 0,
							max: 100,
							from: '<?php echo ($_GET["hr12_from"]); ?>' == '' ? 0 : '<?php echo ($_GET["hr12_from"]); ?>',
							to: '<?php echo ($_GET["hr12_to"]); ?>' == '' ? 100 : '<?php echo ($_GET["hr12_to"]); ?>',
							step:0.1, 
							onFinish: function (data) { //拖动结束回调
								 // console.log(data)
									$('input[name="hr12_from"]').val(data.from);
								 $('input[name="hr12_to"]').val(data.to);
							}
			 
					});
				</script>
				
				<div class="cls"></div>
			</div>


			<ul class="chandi_ul f_14 mt_20 " id="jiaohuodi_ul" style="width:760px;">
				<li>交货地：</li>
				<li >
					<div id="jhd_click">
						<?php if(is_array($jiaohuodi)): foreach($jiaohuodi as $k=>$v): if(strpos($_GET['jhd'], '|'.$v['id'].'-|') !== false): ?><a href="#." class="clr_3 mr_10 xuan_cur" myid="<?php echo ($v["id"]); ?>"><?php echo ($v["name"]); ?></a>
							<?php else: ?>
								<a href="#." class="clr_3 mr_10 " myid="<?php echo ($v["id"]); ?>"><?php echo ($v["name"]); ?></a><?php endif; endforeach; endif; ?>
					</div>
				</li>
			</ul>
			

<div id="jhd_wraper" class="mt_10">
	<?php if(is_array($jiaohuodi)): foreach($jiaohuodi as $key=>$v): ?><span id="jhd_<?php echo ($v["id"]); ?>">
			<?php if($_GET['jhd2'] != '' and $_GET['jhd2'] != '|'): if(is_array($jhd2[$v['id']])): foreach($jhd2[$v['id']] as $key=>$v2): if(strpos($_GET['jhd2'], '|'.$v['id'].'-'.$v2['id'].'-|') !== false): ?><a href="#." class="clr_3 mr_10 xuan_cur f_14" pid="<?php echo ($v["id"]); ?>" myid="<?php echo ($v2["id"]); ?>"><?php echo ($v2["name"]); ?></a>
					<?php else: ?>
						<a href="#." class="clr_3 mr_10 f_14" pid="<?php echo ($v["id"]); ?>" myid="<?php echo ($v2["id"]); ?>"><?php echo ($v2["name"]); ?></a><?php endif; endforeach; endif; endif; ?>
		</span><?php endforeach; endif; ?>
</div>


<script type="text/javascript">
//点击父交货地
	$('#jhd_click').on('click', 'a', function(){
		var that  = $(this);
		if(!that.hasClass('xuan_cur')){
			$.getJSON('<?php echo U("Tools/getJhd");?>', {id:that.attr('myid')}, function(d){
				// console.log(d)
				$.each(d, function(k,v){
					$('#jhd_'+that.attr('myid')).append('<a href="#." pid="'+v.pid+'" myid="'+v.id+'" class="mr_10 clr_3 f_14">'+v.name+'</a>');
				});
				that.addClass('xuan_cur');
			});
		}else{
			that.removeClass('xuan_cur');
		}

		$('#jhd_'+that.attr('myid')+' a[pid="'+that.attr('myid')+'"]').remove();
	});


	//点击子交货地
	$('#jhd_wraper').on('click', 'a', function(){
		var that  = $(this);
		if(!that.hasClass('xuan_cur')){
			that.addClass('xuan_cur');
		}else{
			that.removeClass('xuan_cur');
		}
	});
</script>

			<form action="<?php echo U('index');?>" id="fm1">
				<input id="act" name="act" value="" type="hidden" />
				<div class="dis_n">
					<input name="def" value="<?php echo ($def); ?>" />
					<!-- 父产地 -->
					<input name="cd" value="" />
					<!-- 子产地 -->
					<input name="cd2" value="" />
					<!-- 孙产地 -->
					<input name="cd3" value="" />
					
					<!-- 父交货地-->
					<input name="jhd" value="" placeholder="jhd" />
					<!-- 子交货地 -->
					<input name="jhd2" value=""  placeholder="jhd2"/>

					<textarea id="url" name="url"></textarea>
					<input name="leixing" value="<?php echo ($_GET['leixing']); ?>" />
					<input name="pihao_kunkao" value="<?php echo ($_GET['pihao_kunkao']); ?>" />
					<input name="changdu_from" value="<?php echo ($_GET['changdu_from'] == '' ? 25 : $_GET['changdu_from']); ?>" />
					<input name="changdu_to" value="<?php echo ($_GET['changdu_to'] == '' ? 32 : $_GET['changdu_to']); ?>"/>
					<input name="qiangli_from" value="<?php echo ($_GET['qiangli_from'] == '' ? 25 : $_GET['qiangli_from']); ?>"/>
					<input name="qiangli_to" value="<?php echo ($_GET['qiangli_to'] == '' ? 32 : $_GET['qiangli_to']); ?>"/>
					<input name="mazhi_from" value="<?php if($_GET['mazhi_from'] == ''){echo (2.5);}else{echo ($_GET['mazhi_from']);} ?>"/>
					<input name="mazhi_to" value="<?php if($_GET['mazhi_to'] == ''){echo (5.5);}else{echo ($_GET['mazhi_to']);} ?>"/>
					<input name="huichaolv_from" value="<?php echo ($_GET['huichaolv_from'] == '' ? 0 : $_GET['huichaolv_from']); ?>"/>
					<input name="huichaolv_to" value="<?php echo ($_GET['huichaolv_to'] == '' ? 10 : $_GET['huichaolv_to']); ?>"/>
					<input name="hanzalv_from" value="<?php echo ($_GET['hanzalv_from'] == '' ? 0 : $_GET['hanzalv_from']); ?>"/>
					<input name="hanzalv_to" value="<?php echo ($_GET['hanzalv_to'] == '' ? 6 : $_GET['hanzalv_to']); ?>"/>
					<input name="zhengqidu_from" value="<?php echo ($_GET['zhengqidu_from'] == '' ? 77 : $_GET['zhengqidu_from']); ?>"/>
					<input name="zhengqidu_to" value="<?php echo ($_GET['zhengqidu_to'] == '' ? 90 : $_GET['zhengqidu_to']); ?>"/>
					<input name="bm123_from" value="<?php echo ($_GET['bm123_from'] == '' ? 0 : $_GET['bm123_from']); ?>"/>
					<input name="bm123_to" value="<?php echo ($_GET['bm123_to'] == '' ? 100 : $_GET['bm123_to']); ?>"/>
					<input name="bm45_from" value="<?php echo ($_GET['bm45_from'] == '' ? 0 : $_GET['bm45_from']); ?>"/>
					<input name="bm45_to" value="<?php echo ($_GET['bm45_to'] == '' ? 100 : $_GET['bm45_to']); ?>"/>
					<input name="ddw123_from" value="<?php echo ($_GET['ddw123_from'] == '' ? 0 : $_GET['ddw123_from']); ?>"/>
					<input name="ddw123_to" value="<?php echo ($_GET['ddw123_to'] == '' ? 100 : $_GET['ddw123_to']); ?>"/>
					<input name="dhr123_from" value="<?php echo ($_GET['dhr123_from'] == '' ? 0 : $_GET['dhr123_from']); ?>"/>
					<input name="dhr123_to" value="<?php echo ($_GET['dhr123_to'] == '' ? 100 : $_GET['dhr123_to']); ?>"/>
					<input name="hr12_from" value="<?php echo ($_GET['hr12_from'] == '' ? 0 : $_GET['hr12_from']); ?>"/>
					<input name="hr12_to" value="<?php echo ($_GET['hr12_to'] == '' ? 100 : $_GET['hr12_to']); ?>"/>
				</div>
				<ul class="chandi_ul f_14 mt_20">
					<li>
						交货仓库：<input class="border1 f_14 clr_9 pad_5" placeholder="请输入仓库名称"  
							value="<?php echo ($_GET['cangchumingcheng']); ?>" name="cangchumingcheng" style="width:350px;" /> 
						轧花厂：<input class="border1 f_14 clr_9 pad_5" placeholder="请输入轧花厂名称或编号" 
							value="<?php echo ($_GET['jiagongchang']); ?>" name="jiagongchang"  style="width:350px;" /> 
						<input type="button" id="tijiaoClick" value="✓ 确认筛选　" class="f_14 clr_f bg_lan border0 pad_5" />
					<!-- 	<a href="#." id="add_dingzhi" style=" font-weight: 700;
						color: #F1830E;
						padding-left: 10px;
						font-size: 15px;">+添加到个性需求</a> -->
						<!-- <a href="#" class="clr_ora f_wei">+添加到个性需求</a> -->
					</li>
				</ul>

				<div class="dis_n">
					<input name="hot" value="<?php echo ($_GET['hot']); ?>" />
					<input name="zuiyou" value="<?php echo ($_GET['zuiyou']); ?>" />
					<input name="xiangtong"  value="<?php echo ($_GET['xiangtong']); ?>" />
					<input name="dan"  value="<?php echo ($_GET['dan']); ?>" />
					<input name="jicha"  value="<?php echo ($_GET['jicha']); ?>" />
				</div>
			</form>


			<script type="text/javascript">
			$('#add_dingzhi').click(function(){
				setForm();
				$('#url').val($('#fm1').serialize());
				$.post('<?php echo U("rDingzhi");?>',$('#fm1').serialize(), function(d){
					alert(d.msg);
					if(d.code==1){
						location.href="<?php echo U('Center/dingzhi');?>";
					}
				},'json');
			});

				function setForm(){
					//提交表单，而不是导出excel
					$('#act').val('');

					//所选的父产地
					var cd = '|';
					$('#chandi_click a').each(function(k, v){
						if($(this).hasClass('xuan_cur')){
							// cd += $(this).text() + '|';
							// console.log('v----'+$(this).attr('myid'));
							cd += $(this).attr('myid') + '-|';
						}
					});

					$('input[name="cd"]').val(cd);	

					
					//所选的子产地
					var cd2 = '|';
					$('#child_wraper a').each(function(k, v){
						if($(this).hasClass('xuan_cur')){
							// cd2 += $(this).text() + '|';
							// console.log('v----'+$(this).attr('myid'));
							cd2 += $(this).attr('pid')+'-'+$(this).attr('myid') + '-|';
						}
						
					});

					$('input[name="cd2"]').val(cd2);

					//所选的孙产地
					var cd3 = '|';
					$('#grandson_wraper a').each(function(k, v){
						if($(this).hasClass('xuan_cur')){
							// cd3+= $(this).text() + '|';
							// console.log('v----'+$(this).attr('myid'));
							cd3+= $(this).attr('gpid')+'-'+$(this).attr('pid')+'-'+$(this).attr('myid')+ '-|';
						}
						
					});

					$('input[name="cd3"]').val(cd3);


					//所选的父交货地
					var jhd = '|';
					$('#jhd_click a').each(function(k, v){
						if($(this).hasClass('xuan_cur')){
							// cd += $(this).text() + '|';
							// console.log('v----'+$(this).attr('myid'));
							jhd += $(this).attr('myid') + '-|';
						}
					});

					$('input[name="jhd"]').val(jhd);	

					
					//所选的子交货地
					var jhd2 = '|';
					$('#jhd_wraper a').each(function(k, v){
						if($(this).hasClass('xuan_cur')){
							// cd2 += $(this).text() + '|';
							// console.log('v----'+$(this).attr('myid'));
							jhd2 += $(this).attr('pid')+'-'+$(this).attr('myid') + '-|';
						}
						
					});

					$('input[name="jhd2"]').val(jhd2);
				}


				//提交表单
				$('#tijiaoClick').click(function(){
					setForm();
					$('#fm1').submit();
				});
			</script>
		</div>



<div class="mt_20 fl">
	<form action="<?php echo U('index');?>" id="fms">
		<input lay-verify="required"  name="pihao" id="key" value="<?php echo ($_GET['pihao']); ?>" placeholder="搜索批号/捆号" class="layui-input" style="width:200px;float: left;" />
		<input type="submit" id="search_click" value="搜索" class="layui-btn" style="float: left;" />
		<div style="clear: both;"></div>
	</form>
	<script>
		$('#search_click').click(function(){
			if($('#key').val()==''){
				alert('请输入关键词');
				$('#key').focus();
				return false;
			}
		});
	</script>
</div>

<div class="mt_20 fl ml_20 " style="position: relative;">
	<div style="margin-top:10px;" ><a href="#." class="clr_r" id="delClick">选择要删除的文件</a></div>
	<div id="file_show" style="display:none;position: absolute;bottom:20px;left:0;background: #fff;padding:5px 10px;width:300px;line-height:24px;box-sizing:border-box;border:1px solid #eee;z-index: 19;">
	<form id="fm_del">
		<ul style="height: 250px;overflow-y: auto;">
		<?php if(is_array($excels)): foreach($excels as $key=>$v): ?><li><label><input type="checkbox" value="<?php echo ($v["id"]); ?>" name="id[]" /> <span><?php echo ($v["title"]); ?>(<?php echo (date("Y/m/d",$v["add_time"])); ?>)</span></label></li><?php endforeach; endif; ?>
		</ul>
		<input type="submit" value="确认删除" />
	</form>
	</div>
</div>

<script type="text/javascript">
	$('#delClick').click(function(){
		$('#file_show').toggle();
	});
	$('#fm_del').submit(function(e){
		e.preventDefault();
		if($('#fm_del input[type="checkbox"]').is(':checked')){
			$.post('<?php echo U("runDelFile");?>', $('#fm_del').serialize(), function(d){
				console.log(d);
					alert(d.msg);
					if(d.code==1){
						history.go(0);
					}
			},'json');
		}else{
			alert('请选择要删除的文件');
			return false;
		}
	});
</script>
<div style="clear: both;"></div>

<div class="mt_20">
	<!-- <button id="add_click" class="layui-btn" > <i class="fa fa-plus-circle" aria-hidden="true"></i> 添加</button> -->
	<button id="del_click" class="layui-btn" style="background:#ff6c60;" ><i class="fa fa-trash-o" aria-hidden="true" ></i> 删除</button>
	<input type="button" class="go_up layui-btn" value="导入Excel"  />
	<button id="update_premium_click" class="layui-btn" style="background:#1890ff;" ><i class="fa fa-calculator" aria-hidden="true" ></i> 批量更新升贴水</button>

	<select name="zhuangtai" id="zhuangtai_all" style="height: 35px;padding:0;width:120px;" >
		<option value="">批量更新状态</option>
	
		<?php if(is_array($xls_zhuangtai)): foreach($xls_zhuangtai as $key=>$v2): ?><option value="<?php echo ($v2); ?>" <?php if($v2 == $v['zhuangtai']): ?>selected<?php endif; ?> ><?php echo ($v2); ?></option><?php endforeach; endif; ?>
	</select>

	<script>

	$(function(){
		$('#add_click').click(function(){
			var aa = "<?php echo U('add', array('cid'=>$_GET['cid'], 'type'=>$_GET['type']));?>";
			x_admin_show('添加内容', aa);
		});

		$('#zhuangtai_all').change(function(){
			if($(this).val()!=''){
				if(!$('#fm2 input[name="id[]"]').is(':checked')){
					alert('请选择要更新的项');
					$("#zhuangtai_all").prop('selectedIndex', 0)
				}else{
					if(confirm('确认更新状态?')){
						$('#zhuangtai_text').val($(this).val());
						// console.log($('#fm2').serialize());
						$.post('<?php echo U("changeZhuangtaiAll");?>', $('#fm2').serialize(), function(d){
							if(d.code==1){
								history.go(0);
							}
						},'json');
					}
				}
			}
		});
	});

	</script>
</div>


	<div style="clear: both;"></div>
	<form id="fm2">
		<input name="zhuangtai_text" id="zhuangtai_text" type="hidden" />
		<div style="width:1400px;overflow-x: auto">
			<table class="tbl2 bg_f w_100 rad_10 mt_10 clr_79" style="width:1500px;">
				<thead>
					<tr style="text-align: left;">
						<th>操作</th>
						<th><label id="checkAll"><input type="checkbox" class="ver_mid" /> <span class="ver_mid">全选</span></label></th>
						<!-- <th>序号</th> -->
						<th>批号/捆号</th>
						<th>加工类型</th>
						<th>状态</th>
						<th >颜色级/品级</th>
						<th>平均马值</th>
						<th>长度</th>
						<th>强力</th>
						<th>含杂率</th>
						<th>回潮率</th>
						<th>公重(吨)</th>
						<th>毛重(吨)</th>
						<th>长度整齐度</th>
						<th>轧工质量</th>
						<th>加工厂</th>
						<th>仓库</th>
						<th>基差</th>
						<th>升贴水</th>
						<th>调整后基差</th>
						<th>备注</th>
						<th>点价合约</th>
						<th>包数</th>
						<!-- <th>基差</th> -->
						<!-- <th>公重</th> -->
						
					</tr>
				</thead>
				<tbody>
					<?php if(is_array($lists)): foreach($lists as $key=>$v): ?><tr>
							<td>
								<a class="bg_g block_a edit_item" href="javascript:;" my_url="<?php echo U('edit', array('id'=>$v['id'], 'cid'=>$v['cat_id'], 'type'=>$_GET['type']));?>" title="编辑" >
									<i class="fa fa-pencil-square-o" aria-hidden="true"  style="font-size: 14px;color:#fff;"></i>
								</a>
								&nbsp;
								<a class="bg_r block_a del_item" href="javascript:;" my_url="<?php echo U('del', array('id'=>$v['id']));?>" title="删除" >
									<i class="fa fa-trash-o" aria-hidden="true" style="font-size: 14px;color:#fff;"></i>
								</a>
							</td>
							<td>
								<label>
									<input type="checkbox" name="id[]" value="<?php echo ($v["id"]); ?>" class="ver_mid per_check" />
									<!-- <span class="ver_mid"><?php echo ($v["id"]); ?></span> -->
								 </label>
							</td>
							<!-- <td><?php echo ($v["xvhao"]); ?></td> -->
							<td><?php echo ($v["pihao_kunkao"]); ?></td>
							<td><?php echo ($v["leixing"]); ?></td>
							<td>
								<select name="zhuangtai" class="zhuangtai" style="height: 35px;padding:0;" myid="<?php echo ($v["id"]); ?>">
									<option value="">选择状态</option>
								
									<?php if(is_array($xls_zhuangtai)): foreach($xls_zhuangtai as $key=>$v2): ?><option value="<?php echo ($v2); ?>" <?php if($v2 == $v['zhuangtai']): ?>selected<?php endif; ?> ><?php echo ($v2); ?></option><?php endforeach; endif; ?>
								</select>
							</td>
							<td><div  class="ellipsis1" style="width:90px;"><?php echo ($v["yanseji_pinji"]); ?></div></td>
							<td><?php echo ($v["mazhi"]); ?></td>
							<td><?php echo ($v["changdu"]); ?></td>
							<td><?php echo ($v["qiangli"]); ?></td>
							<td><?php echo ($v["hanzalv"]); ?></td>
							<td><?php echo ($v["huichaolv"]); ?></td>
							<td><?php echo ($v["gongzhong"]); ?></td>
							<td><?php echo ($v["maozhaong"]); ?></td>
							<td><?php echo ($v["zhengqidu"]); ?></td>
							<td><?php echo ($v["ygzl"]); ?></td>
							<td><div class="ellipsis1" style="width:90px;"><?php echo ($v["jiagongchang"]); ?></div></td>
							<td><div class="ellipsis1" style="width:90px;"><?php echo ($v["cangchumingcheng"]); ?></div></td>
							<td><?php echo ($v["jicha"]); ?></td>
							<td>
								<?php if($v["premium_discount"] > 0): ?><span style="color: #52c41a;">+<?php echo ($v["premium_discount"]); ?></span>
								<?php elseif($v["premium_discount"] < 0): ?>
									<span style="color: #ff4d4f;"><?php echo ($v["premium_discount"]); ?></span>
								<?php else: ?>
									<span style="color: #666;">0</span><?php endif; ?>
								<a href="javascript:;" onclick="updateSinglePremium(<?php echo ($v["id"]); ?>)" title="更新升贴水" style="margin-left: 5px;">
									<i class="fa fa-refresh" style="color: #1890ff;"></i>
								</a>
							</td>
							<td>
								<?php if($v["calculated_jicha"] > $v.jicha): ?><span style="color: #52c41a; font-weight: bold;"><?php echo ($v["calculated_jicha"]); ?></span>
								<?php elseif($v["calculated_jicha"] < $v.jicha): ?>
									<span style="color: #ff4d4f; font-weight: bold;"><?php echo ($v["calculated_jicha"]); ?></span>
								<?php else: ?>
									<span style="font-weight: bold;"><?php echo ($v["calculated_jicha"]); ?></span><?php endif; ?>
							</td>
							<td><?php echo ($v["beizhu"]); ?></td>
							<td><?php echo ($v["dianjiaheyue"]); ?></td>
							<td><?php echo ($v["baoshu"]); ?></td>
							<!-- <td><?php echo ($v["jicha2"]); ?></td> -->
							<!-- <td><?php echo ($v["gongzhong2"]); ?></td> -->
							<!-- <td><input class="layui-input order_blur clr_79"  value="<?php echo ($v["order_id"]); ?>" myid="<?php echo ($v["id"]); ?>" style="width:50px;"/></td> -->
							
						</tr><?php endforeach; endif; ?>
				</tbody>
			</table>
		</div>
	</form>


<div id="mask_bg" style="background: rgba(0,0,0,0.6);width: 100%;height: 100%;position: fixed;top:0;left:0;z-index: 99;display: none;">

	<div style="background: #fff;padding:40px;border-radius: 10px;width:600px;margin:100px auto;position: relative;">
		<a href="#." id="close_mask" style="color:#333;font-size:40px;position: absolute;top:0;right:10px;">&times;</a>
		<div id="fm_box">
			<div id="file_wraper" style="max-height: 400px;overflow: auto;">
				<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>1]);?>" style="margin-top:10px;">
					01<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_1"></span>
				</form>
				<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>2]);?>" style="margin-top:10px;display:none;">
	02<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_2"></span>
</form>
<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>3]);?>" style="margin-top:10px;display:none;">
	03<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_3"></span>
</form>
<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>4]);?>" style="margin-top:10px;display:none;">
	04<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_4"></span>
</form>
<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>5]);?>" style="margin-top:10px;display:none;">
	05<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_5"></span>
</form>
<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>6]);?>" style="margin-top:10px;display:none;">
	06<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_6"></span>
</form>
<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>7]);?>" style="margin-top:10px;display:none;">
	07<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_7"></span>
</form>
<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>8]);?>" style="margin-top:10px;display:none;">
	08<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_8"></span>
</form>
<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>9]);?>" style="margin-top:10px;display:none;">
	09<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_9"></span>
</form>
<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>10]);?>" style="margin-top:10px;display:none;">
	10<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_10"></span>
</form>
<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>11]);?>" style="margin-top:10px;display:none;">
	11<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_11"></span>
</form>
<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>12]);?>" style="margin-top:10px;display:none;">
	12<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_12"></span>
</form>
<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>13]);?>" style="margin-top:10px;display:none;">
	13<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_13"></span>
</form>
<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>14]);?>" style="margin-top:10px;display:none;">
	14<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_14"></span>
</form>
<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>15]);?>" style="margin-top:10px;display:none;">
	15<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_15"></span>
</form>
<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>16]);?>" style="margin-top:10px;display:none;">
	16<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_16"></span>
</form>
<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>17]);?>" style="margin-top:10px;display:none;">
	17<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_17"></span>
</form>
<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>18]);?>" style="margin-top:10px;display:none;">
	18<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_18"></span>
</form>
<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>19]);?>" style="margin-top:10px;display:none;">
	19<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_19"></span>
</form>
<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>20]);?>" style="margin-top:10px;display:none;">
	20<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_20"></span>
</form>
<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>21]);?>" style="margin-top:10px;display:none;">
	21<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_21"></span>
</form>
<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>22]);?>" style="margin-top:10px;display:none;">
	22<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_22"></span>
</form>
<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>23]);?>" style="margin-top:10px;display:none;">
	23<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_23"></span>
</form>
<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>24]);?>" style="margin-top:10px;display:none;">
	24<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_24"></span>
</form>
<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>25]);?>" style="margin-top:10px;display:none;">
	25<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_25"></span>
</form>
<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>26]);?>" style="margin-top:10px;display:none;">
	26<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_26"></span>
</form>
<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>27]);?>" style="margin-top:10px;display:none;">
	27<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_27"></span>
</form>
<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>28]);?>" style="margin-top:10px;display:none;">
	28<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_28"></span>
</form>
<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>29]);?>" style="margin-top:10px;display:none;">
	29<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_29"></span>
</form>
<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>30]);?>" style="margin-top:10px;display:none;">
	30<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_30"></span>
</form>
<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>31]);?>" style="margin-top:10px;display:none;">
	31<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_31"></span>
</form>
<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>32]);?>" style="margin-top:10px;display:none;">
	32<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_32"></span>
</form>
<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>33]);?>" style="margin-top:10px;display:none;">
	33<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_33"></span>
</form>
<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>34]);?>" style="margin-top:10px;display:none;">
	34<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_34"></span>
</form>
<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>35]);?>" style="margin-top:10px;display:none;">
	35<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_35"></span>
</form>
<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>36]);?>" style="margin-top:10px;display:none;">
	36<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_36"></span>
</form>
<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>37]);?>" style="margin-top:10px;display:none;">
	37<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_37"></span>
</form>
<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>38]);?>" style="margin-top:10px;display:none;">
	38<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_38"></span>
</form>
<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>39]);?>" style="margin-top:10px;display:none;">
	39<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_39"></span>
</form>
<form class="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel',['id'=>40]);?>" style="margin-top:10px;display:none;">
	40<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_40"></span>
</form>
			</div>
			<div style="margin-top:5px;"><a href="#." style="color: #999;" id="addFm">[+]</a></div><br />
			<input type="button" value="确认导入" id="tijiao" style="border:1px solid #eee;padding:5px 10px;" />
		</div>
		
	</div>
</div>

<script type="text/javascript">
$('.go_up').click(function(){
	$('#mask_bg').show();
});


$('#close_mask').click(function(){
	// $('#mask_bg').hide();
	history.go(0);
});
$('.uploadForm').ajaxForm({
	dataType: 'json',  
	// async:false,
	success: function(d){
		if(d.code==1){
			console.log(d.msg);
			$('#txt_'+d.id).html('<span style="color:green;">'+d.msg+'</span>');
			// history.go(0);
		}else{
			alert(d.msg);
			$('#txt_'+d.id).html('停止');
		}
	},
	beforeSubmit:function(){
	}
});  


var fm_count = 1;
$('#addFm').click(function(){
	if(fm_count<40){
		$('#file_wraper form').eq(fm_count).show();
		fm_count++;
	}else{
		alert('超过最大限制数'+fm_count);
	}
	
});


$('#tijiao').click(function(){
	$('.uploadForm').each(function(k,v){
		var that  = $(this);
		if(that.find('input[type="file"]').val() !=''){
			// console.log('正在导入第'+(k+1)+'个文件……');
			$('#txt_'+(k+1)).html('<span style="vertical-align:middle;">导入中</span><img src="/Public/images/loading.gif" style="width:30px;vertical-align:middle;" />');
			that.submit();
			
		}
	});
});

</script>



<!-- <form id="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runImportExcel');?>">
	<input type="file" name="thumbs_hide" id="thumbs_hide"  />
</form> -->

	<div class="pager mt_10">
		<?php echo ($page); ?>
	</div>
</div>
<script>

	$('#del_click').click(function(){
		if(!$('#fm2 input[name="id[]"]').is(':checked')){
			alert('请选择要删除的选项');
			return false;
		}else{
			if(confirm('确认删除?')){
				$.post('<?php echo U("delAll");?>', $('#fm2').serialize(), function(d){
					if(d.code==1){
						history.go(0);
					}
				},'json');
			}
		}
	});



	$('#checkAll').click(function(){
		$('#fm2 input[type=checkbox]').prop('checked', $(this).find('input[type=checkbox]').prop('checked'));
	});
	$('.zhuangtai').change(function(){
		if($(this).val()!=''){
			$.getJSON('<?php echo U("changeZhuangtai");?>', {id:$(this).attr('myid'), zhuangtai:$(this).val()}, function(d){
				if(d.code==1){
					history.go(0);
				}
			});
		}
	});
	$('.edit_item').click(function(){
		x_admin_show('编辑',$(this).attr('my_url'));
	});
	$('.del_item').click(function(){
		var aa = $(this);
		layer.confirm('确认删除？', {icon: 3, title:'提示'}, function(index){
			$.getJSON(aa.attr('my_url'), function(d){
				if(d.code==1){
					history.go(0);
				}
			});
			layer.close(index);
		});
	});
	$('.order_blur').blur(function(){
		$.getJSON('<?php echo U("runOrder");?>',{id:$(this).attr('myid'), order_id:$(this).val()}, function(d){if(d.code==1)history.go(0);});
	});

	// 批量更新升贴水
	$('#update_premium_click').click(function(){
		if(!$('#fm2 input[name="id[]"]').is(':checked')){
			alert('请选择要更新升贴水的记录');
			return false;
		}else{
			if(confirm('确认批量更新选中记录的升贴水？')){
				var loadIndex = layer.load(2, {content: '正在计算升贴水...'});
				$.post('<?php echo U("updatePremiumDiscountBatch");?>', $('#fm2').serialize(), function(d){
					layer.close(loadIndex);
					if(d.code==1){
						layer.msg(d.msg, {icon: 1});
						setTimeout(function(){
							history.go(0);
						}, 1500);
					} else {
						layer.msg(d.msg || '更新失败', {icon: 2});
					}
				},'json');
			}
		}
	});

	// 单条记录更新升贴水
	function updateSinglePremium(id){
		if(confirm('确认更新此记录的升贴水？')){
			var loadIndex = layer.load(2, {content: '正在计算升贴水...'});
			$.getJSON('<?php echo U("updatePremiumDiscountSingle");?>', {id: id}, function(d){
				layer.close(loadIndex);
				if(d.code==1){
					layer.msg('升贴水更新成功', {icon: 1});
					setTimeout(function(){
						history.go(0);
					}, 1000);
				} else {
					layer.msg(d.msg || '更新失败', {icon: 2});
				}
			});
		}
	}
</script>
</body>
</html>
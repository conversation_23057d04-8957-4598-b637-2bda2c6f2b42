<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">

<title>后台登录</title>

<meta name="renderer" content="webkit|ie-comp|ie-stand">

<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">

<meta http-equiv="Cache-Control" content="no-siteapp" />

<link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />

<link rel="stylesheet" href="/Public/xadmin/css/font.css">

<link rel="stylesheet" href="/Public/xadmin/css/xadmin.css">

<script type="text/javascript" src="/Public/xadmin/js/jquery.min.js"></script>

<script src="/Public/xadmin/lib/layui/layui.js" charset="utf-8"></script>

<script type="text/javascript" src="/Public/xadmin/js/xadmin.js"></script>

<link rel="stylesheet" href="/Public/css/back2.css">

<link rel="stylesheet" href="/Public/font-awesome/css/font-awesome.min.css">


</head>
<body>
    <div class="x-body">
        <button id="add_click" class="layui-btn"><i class="layui-icon"></i>添加条件字段</button>
        
        <script>
            $('#add_click').click(function(){
                var aa = "<?php echo U('add');?>";
                x_admin_show('添加条件字段', aa);
            });
        </script>

        <table class="tbl2 bg_f w_100 rad_10 mt_10 clr_79">
            <thead>
                <tr>
                    <th class="al_lt">字段代码</th>
                    <th class="al_lt">字段名称</th>
                    <th class="al_lt">字段类型</th>
                    <th class="al_lt">单位</th>
                    <th class="al_lt">支持范围</th>
                    <th class="al_lt">选择项</th>
                    <th class="al_lt">状态</th>
                    <th class="al_lt">排序</th>
                    <th class="al_lt">操作</th>
                </tr>
            </thead>
            <tbody>
                <?php if(is_array($fields)): foreach($fields as $key=>$v): ?><tr>
                        <td>
                            <code><?php echo ($v["field_code"]); ?></code>
                            （ID：<?php echo ($v["id"]); ?>）
                        </td>
                        <td>
                            <a href="javascript:;" my_url="<?php echo U('edit',array('id'=>$v['id']));?>" class="edit_item" title="编辑" style="font-size:14px;">
                                <?php echo ($v["field_name"]); ?>
                            </a>
                        </td>
                        <td>
                            <?php if($v['field_type'] == 'number') { echo '<span class="layui-badge layui-bg-blue">' . $field_types[$v['field_type']] . '</span>'; } elseif($v['field_type'] == 'select') { echo '<span class="layui-badge layui-bg-orange">' . $field_types[$v['field_type']] . '</span>'; } else { echo '<span class="layui-badge layui-bg-gray">' . $field_types[$v['field_type']] . '</span>'; } ?>
                        </td>
                        <td>
                            <?php if($v['field_unit']): echo ($v["field_unit"]); ?>
                            <?php else: ?>
                                -<?php endif; ?>
                        </td>
                        <td>
                            <?php if($v['is_range'] == 1): ?><span class="layui-badge layui-bg-green">支持</span>
                            <?php else: ?>
                                <span class="layui-badge">不支持</span><?php endif; ?>
                        </td>
                        <td style="max-width: 200px; word-break: break-all;">
                            <?php echo ($v["select_options_display"]); ?>
                        </td>
                        <td>
                            <?php if($v['is_enabled'] == 1): ?><span class="layui-badge layui-bg-green">启用</span>
                            <?php else: ?>
                                <span class="layui-badge">禁用</span><?php endif; ?>
                        </td>
                        <td>
                            <input class="layui-input sort_blur" myid="<?php echo ($v["id"]); ?>" value="<?php echo ($v["sort_order"]); ?>" style="width:60px;" />
                        </td>
                        <td>
                            <a href="javascript:;" my_url="<?php echo U('edit',array('id'=>$v['id']));?>" class="edit_item layui-btn layui-btn-xs" title="编辑">编辑</a>
                            <a href="javascript:;" my_url="<?php echo U('toggleStatus', array('id'=>$v['id']));?>" class="toggle_item layui-btn layui-btn-xs <?php if($v["is_enabled"] == 1): ?>layui-btn-warm<?php else: ?>layui-btn-normal<?php endif; ?>" title="切换状态">
                                <?php if($v['is_enabled'] == 1): ?>禁用<?php else: ?>启用<?php endif; ?>
                            </a>
                            <a href="javascript:;" my_url="<?php echo U('del', array('id'=>$v['id']));?>" class="del_item layui-btn layui-btn-xs layui-btn-danger" title="删除">删除</a>
                        </td>
                    </tr><?php endforeach; endif; ?>
            </tbody>
        </table>

        <script>
            $('.edit_item').click(function(){
                x_admin_show('编辑条件字段', $(this).attr('my_url'));
            });
            
            $('.toggle_item').click(function(){
                var aa = $(this);
                layer.confirm('确认切换状态？', {icon: 3, title:'提示'}, function(index){
                    $.getJSON(aa.attr('my_url'), function(d){
                        if(d.code==1){
                            layer.msg(d.msg, {icon: 1});
                            setTimeout(function(){
                                history.go(0);
                            }, 1000);
                        }else{
                            layer.msg(d.msg, {icon: 2});
                        }
                    });
                    layer.close(index);
                });
            });
            
            $('.del_item').click(function(){
                var aa = $(this);
                layer.confirm('确认删除？删除后不可恢复！', {icon: 3, title:'提示'}, function(index){
                    $.getJSON(aa.attr('my_url'), function(d){
                        if(d.code==1){
                            layer.msg(d.msg, {icon: 1});
                            setTimeout(function(){
                                history.go(0);
                            }, 1000);
                        }else{
                            layer.msg(d.msg, {icon: 2});
                        }
                    });
                    layer.close(index);
                });
            });
            
            $('.sort_blur').blur(function(){
                if($(this).val()!=''){
                    $.getJSON('<?php echo U("updateSort");?>', {id:$(this).attr('myid'), sort_order:$(this).val()}, function(d){
                        if(d.code==1){
                            layer.msg(d.msg, {icon: 1});
                        }
                    });
                }
            });
        </script>
    </div>
</body>
</html>
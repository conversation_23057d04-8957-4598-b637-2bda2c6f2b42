<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>
<html>
<head>

	<title>青岛三匹马实业</title>
	<meta name="keywords" content="青岛三匹马实业" />
	<meta name="description" content="青岛三匹马实业" />
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

<script type="text/javascript" src="/Public/js/jquery-1.11.1.min.js"></script>

<script type="text/javascript" src="/Public/js/swiper.min.js"></script>

<link rel="stylesheet" type="text/css" href="/Public/css/css.css" />

<link rel="stylesheet" type="text/css" href="/Public/css/swiper.min.css">
<link rel="icon" type="image/x-icon" href="/Public/images/favicon.ico">

</head>

<body>
	<div class="bg_f min_w">
	<div class="m_auto padt_15 clearfix ">
		<div class="fl "><a href="<?php echo U('Index/index');?>"><img src="/Public/images/logo.png" class="dis_b" style="height:78px;" /></a></div>
		<div class="fl ml_50 pos_rela"  >
			<div style="position: absolute;top:55px;left:0;" class="dis_n" id="pl_box">
				<textarea style="width:400px;height: 150px;" id="pl_text" placeholder="每行输入一个批次/捆号" class="border1 dis_b pad_10"></textarea>
				<input type="button" id="pl_sou" value="搜索" class="dis_b bg_ora" style="color: #fff;padding:5px 10px;border:0" />
			</div>
			<form action="<?php echo U('Chaoshi/index');?>" id="ss_fm"  style="margin-top:9px;">
					<input type="search" name="sk" id="sousuo_sk" class="clr_9" value="<?php echo ($_GET['sk']); ?>" autocomplete="off" 
						placeholder="批号/轧花厂/仓库/产棉地区" style="height: 40px;border:2px solid #28afe5;width:500px;padding:10px;box-sizing: border-box;">
					<button type="submit" class="send" style="height: 40px;outline: none;cursor: pointer;">
						<img src="/Public/images/searth.png" style="width:20px;">
					</button>
					<span class="f_14 clr_r pointer" id="pl_click" style="position: absolute;top:30px;right:80px;">批量</span>
		
			</form>
		</div>

		<script>
		$('#pl_click').click(function(){
			$('#pl_box').toggle();
		});

			$('#pl_sou').click(function(){
				var ss = $('#pl_text').val().trim();
				var arr = ss.split(/\n/g);
				var xx = [];
				for(var i = 0; i<arr.length; i++){
					if(arr[i].trim()!=''){
						xx.push(arr[i].trim());
					}
				}
				// console.log(xx.toString());
				$('#sousuo_sk').val(xx.toString());
				$('#ss_fm').submit();
			});
		</script>


		<div class="fr al_rt f_14 login ">
			<?php if($session_user["id"] > 0): ?><div class="mt_10">
					<a href="<?php echo U('Center/info');?>" class="clr_b f_16 log_a">会员中心</a>
					<a href="<?php echo U('Passport/exits');?>" class="clr_b f_16">退出</a>
				</div>
			<?php else: ?>
				<div class="mt_10">
					<a href="<?php echo U('Passport/reg');?>" class="clr_d f_16">注册</a>
					<a href="<?php echo U('Passport/login');?>" class="clr_d f_16 log_a">登录</a>
				</div><?php endif; ?>
			
		</div>
	</div>
</div>


	

		<div class="m_auto border1 reg_bg" >
			<div class="pad_20"></div>
			<div class="reg_box">
				<form id="fm1">
					<div class="border1 pad_10 mt_20">
						<img src="/Public/images/mobile.png" class="ver_mid" />
						<input class="f_14 clr_9 border0 ver_mid ml_10" name="username" id="username" placeholder="手机号" />
					</div>
					<div class="border1 pad_10 mt_20 pos_rela">
						<img src="/Public/images/mobile.png" class="ver_mid" />
						<input class="f_14 clr_9 border0 ver_mid ml_10" name="mobile_code" placeholder="手机验证码" style="width:100px;" />
						<input type="button" value="获取验证码" class="bg_b clr_f border0 pad_10"  id="getcode" style="position: absolute;top:10px;right:10px;" />
					</div>
					<div class="border1 pad_10 mt_20">
						<img src="/Public/images/user.png" class="ver_mid" />
						<input class="f_14 clr_9 border0 ver_mid ml_10" name="nickname" id="nickname" placeholder="姓名" />
					</div>
					<div class="border1 pad_10 mt_20">
						<img src="/Public/images/gsmc.png" class="ver_mid" />
						<input class="f_14 clr_9 border0 ver_mid ml_10" name="company" id="company" placeholder="公司名称" />
					</div>
					<div class="border1 pad_10 mt_20">
						<img src="/Public/images/password.png" class="ver_mid" />
						<input class="f_14 clr_9 border0 ver_mid ml_10" name="password" id="password" type="password" placeholder="密码" placeholder="密码" />
					</div>
					<div class="border1 pad_10 mt_20">
						<img src="/Public/images/password.png" class="ver_mid" />
						<input class="f_14 clr_9 border0 ver_mid ml_10" style="background: transparent;" 
							name="password2" id="password2" type="password" placeholder="重复密码" placeholder="密码" />
					</div>
	<!-- 				<div class="mt_20">
						<input placeholder="验证码" name="captcha" id="captcha" class="f_16 clr_9  ver_mid border1 pad_10" style="width:120px;" />
						<img src="<?php echo U('Tools/verify');?>" class="ver_mid" id="verify" />
					</div> -->
					<div class="  mt_10">
						<label>
							<input type="checkbox" class="ver_mid" id="agreement" /> 
							<span class="ver_mid f_14 clr_9 ver_mid" >已阅读并同意 <a href="<?php echo U('agreement');?>" class="clr_b" >《用户服务协议》</a></span>
						</label>
					</div>


					<div class=" padt_30">
							<input type="submit" value="注册" class="f_16 clr_f bg_b w_100p border0 padt_10" />
							<div class="f_14 al_rt mt_10">已有账号，<a href="<?php echo U('login');?>" class="clr_b">立即登录</a></div>
						</div>
				</form>

				<script type="text/javascript">

				$(function(){

$('#getcode').click(function(){
	if(!/^1\d{10}$/.test($('#username').val())){
		alert('请输入正确的手机号');	
		return false;
	}
	
	$.getJSON("<?php echo U('Tools/getCodeAly');?>", {mobile:$('#username').val()}, function(d){
		if(d.code==1){
			alert(d.msg);
			time($('#getcode'));
		}else{
			alert(d.msg);
		}
	});		
});
			
var wait=120;
function time(o) {
	if (wait == 0) {
			o.attr("disabled",false);
			o.val("获取验证码");
			wait = 120;
	} else {
			o.attr("disabled", true);
			o.val(wait+"秒");
			wait--;
			setTimeout(function() {
					time(o)
			},
			1000)
	}
}


					$('#verify').click(function(){
						$(this).attr('src', "<?php echo U('Tools/verify');?>");
					});

					$('#fm1').submit(function(e){

						e.preventDefault();
						if(!/^1\d{10}$/.test($('#username').val())){
							alert('请输入正确的手机号码');
							$('#username').focus();
							return false;
						}


						if(/^[ ]*$/.test($('#nickname').val())){
							alert('请输入姓名');
							$('#nickname').focus();
							return false;
						}

						if(/^[ ]*$/.test($('#company').val())){
							alert('请输入公司名称');
							$('#company').focus();
							return false;
						}

						if(/^[ ]*$/.test($('#password').val())){
							alert('请输入密码');
							$('#password').focus();
							return false;
						}


						if($('#password2').val() != $('#password').val()){
							alert('密码输入不一致');
							$('#password2').focus();
							return false;
						}


						if(/^[ ]*$/.test($('#captcha').val())){
							alert('请输入验证码');
							$('#captcha').focus();
							return false;
						}

						if(!$('#agreement').prop('checked')){
							alert('请阅读并同意《用户服务协议》');
							return false;
						}


						$.post('<?php echo U("runReg");?>', $('#fm1').serialize(), function(d){
							if(d.code== 1){
								alert(d.msg);
								location.href=d.url;
							}else{
								alert(d.msg);
								$('#verify').click();
							}
						},'json');
					});

				});

				
				</script>

			</div>

			<div class="pad_20"></div>
		</div>



<div class="min_w">
<div class="f_14 clr_9 al_ct padt_10 lh_28">

版权所有：青岛三匹马实业有限公司&nbsp;&nbsp;&nbsp;&nbsp;地址：青岛市黄岛区江山南路450号（富安国际大厦）&nbsp;&nbsp;&nbsp;&nbsp;电话：17866631857<br/>



	  Copyright &copy; 2021 鲁ICP备2021017032号-1



 </div>
 
  
<div class="side">
	<ul>
		
		<li class="sideewm"><i class="bgs3"></i>官方微信
			<div class="ewBox son"></div>
		</li>
		<li class="sideetel"><i class="bgs4"></i>联系电话
			<div class="telBox son">
			
				<dd class="bgs2"><span>手机</span>19853227218</dd>
			</div>
		</li>
	
		<li class="sidetop" onClick="goTop()"><i class="bgs6"></i>返回顶部</li>
	</ul>
</div>

<script src="/Public/js/jquery-1.11.0.min.js" type="text/javascript" charset="utf-8"></script>
<script>
	function goTop() {
		$('html,body').animate({
			scrollTop: 0
		}, 500)
	}
</script>

 <style>
 .side{position:fixed;width:60px;right:0;top:60%;margin-top:-200px;z-index:100;border:1px solid #e0e0e0;background:#fff;border-bottom:0}
.side ul li{width:60px;height:70px;float:left;position:relative;border-bottom:1px solid #e0e0e0;color:#333;font-size:14px;line-height:38px;text-align:center;transition:all .3s;cursor:pointer}
.side ul li:hover{background:#f67524;color:#fff}
.side ul li:hover a{color:#fff}
.side ul li i{height:25px;margin-bottom:1px;display:block;overflow:hidden;background-repeat:no-repeat;background-position:center center;background-size:auto 25px;margin-top:14px;transition:all .3s}
.side ul li i.bgs1{background-image:url(/Public/images/right_pic5.png)}
.side ul li i.bgs2{background-image:url(/Public/images/right_pic7.png)}
.side ul li i.bgs3{background-image:url(/Public/images/right_pic2.png)}
.side ul li i.bgs4{background-image:url(/Public/images/right_pic1.png)}
.side ul li i.bgs5{background-image:url(/Public/images/right_pic3.png)}
.side ul li i.bgs6{background-image:url(/Public/images/right_pic6_on.png)}
.side ul li:hover i.bgs1{background-image:url(/Public/images/right_pic5_on.png)}
.side ul li:hover i.bgs2{background-image:url(/Public/images/right_pic7_on.png)}
.side ul li:hover i.bgs3{background-image:url(/Public/images/right_pic2_on.png)}
.side ul li:hover i.bgs4{background-image:url(/Public/images/right_pic1_on.png)}
.side ul li:hover i.bgs5{background-image:url(/Public/images/right_pic3_on.png)}
.side ul li .sidebox{position:absolute;width:78px;height:78px;top:0;right:0;transition:all .3s;overflow:hidden}
.side ul li.sidetop{background:#f67524;color:#fff}
.side ul li.sidetop:hover{opacity:.8;filter:Alpha(opacity=80)}
.side ul li.sideewm .ewBox.son{width:238px;display:none;color:#363636;text-align:center;padding-top:212px;position:absolute;left:-240px;top:0;background-image:url(/Public/images/leftewm.png);background-repeat:no-repeat;background-position:center center;border:1px solid #e0e0e0}
.side ul li.sideetel .telBox.son{width:240px;height:214px;display:none;color:#fff;text-align:left;position:absolute;left:-240px;top:-72px;background:#f67524}
.side ul li.sideetel .telBox dd{display:block;height:118.5px;overflow:hidden;padding-left:82px;line-height:24px;font-size:18px}
.side ul li.sideetel .telBox dd span{display:block;line-height:28px;height:28px;overflow:hidden;margin-top:32px;font-size:18px}
.side ul li.sideetel .telBox dd.bgs1{background:url(/Public/images/right_pic8.png) 28px center no-repeat;background-color:#e96410}
.side ul li.sideetel .telBox dd.bgs2{background:url(/Public/images/right_pic9.png) 28px center no-repeat}
.side ul li:hover .son{display:block!important;animation:fadein 1s}
@keyframes fadein{from{opacity:0}
to{opacity:1}
}
</style>
</div>



</body>


</html>
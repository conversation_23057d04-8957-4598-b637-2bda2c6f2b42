<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>

<html>

<head>

<title>棉花棠</title>

<meta name="keywords" content="棉花棠" />

<meta name="description" content="棉花棠" />

<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

<script type="text/javascript" src="/Public/js/jquery-1.11.1.min.js"></script>

<script type="text/javascript" src="/Public/js/swiper.min.js"></script>

<link rel="stylesheet" type="text/css" href="/Public/css/css.css" />

<link rel="stylesheet" type="text/css" href="/Public/css/swiper.min.css">
<link rel="icon" type="image/x-icon" href="/Public/images/favicon.ico">



</head>



<body class="bg_f2">

<div class="bg_f min_w">
	<div class="m_auto padt_15 clearfix ">
		<div class="fl "><a href="<?php echo U('Index/index');?>"><img src="/Public/images/logo.png" class="dis_b" style="height:78px;" /></a></div>
		<div class="fl ml_50 pos_rela"  >
			<div style="position: absolute;top:55px;left:0;" class="dis_n" id="pl_box">
				<textarea style="width:400px;height: 150px;" id="pl_text" placeholder="每行输入一个批次/捆号" class="border1 dis_b pad_10"></textarea>
				<input type="button" id="pl_sou" value="搜索" class="dis_b bg_ora" style="color: #fff;padding:5px 10px;border:0" />
			</div>
			<form action="<?php echo U('Chaoshi/index');?>" id="ss_fm"  style="margin-top:9px;">
					<input type="search" name="sk" id="sousuo_sk" class="clr_9" value="<?php echo ($_GET['sk']); ?>" autocomplete="off" 
						placeholder="批号/轧花厂/仓库/产棉地区" style="height: 40px;border:2px solid #28afe5;width:500px;padding:10px;box-sizing: border-box;">
					<button type="submit" class="send" style="height: 40px;outline: none;cursor: pointer;">
						<img src="/Public/images/searth.png" style="width:20px;">
					</button>
					<span class="f_14 clr_r pointer" id="pl_click" style="position: absolute;top:30px;right:80px;">批量</span>
		
			</form>
		</div>

		<script>
		$('#pl_click').click(function(){
			$('#pl_box').toggle();
		});

			$('#pl_sou').click(function(){
				var ss = $('#pl_text').val().trim();
				var arr = ss.split(/\n/g);
				var xx = [];
				for(var i = 0; i<arr.length; i++){
					if(arr[i].trim()!=''){
						xx.push(arr[i].trim());
					}
				}
				// console.log(xx.toString());
				$('#sousuo_sk').val(xx.toString());
				$('#ss_fm').submit();
			});
		</script>


		<div class="fr al_rt f_14 login ">
			<?php if($session_user["id"] > 0): ?><div class="mt_10">
					<a href="<?php echo U('Center/info');?>" class="clr_b f_16 log_a">会员中心</a>
					<a href="<?php echo U('Passport/exits');?>" class="clr_b f_16">退出</a>
				</div>
			<?php else: ?>
				<div class="mt_10">
					<a href="<?php echo U('Passport/reg');?>" class="clr_d f_16">注册</a>
					<a href="<?php echo U('Passport/login');?>" class="clr_d f_16 log_a">登录</a>
				</div><?php endif; ?>
			
		</div>
	</div>
</div>



	

<div class="bg_b min_w">
	<div class="m_auto clearfix">
		<ul class="nav_ul clearfix f_16  fl">
			<li><a id="n1" href="<?php echo U('Index/index');?>" class="clr_f">每日精选</a></li>
			<li><a id="n2"  href="<?php echo U('Chaoshi/index');?>" class="clr_f">棉花商城</a></li>
			<li><a id="n3" href="<?php echo U('Center/dingzhi');?>" class="clr_f">个性需求</a></li>
 			<li><a id="n4" href="<?php echo U('Center/favor');?>" class="clr_f">我的收藏</a></li>
			<!--<li><a id="n5" href="<?php echo U('About/contact');?>" class="clr_f">联系我们</a></li> -->
		</ul>

		<div class="f_16 clr_f nav_time fr">欢迎您，今天是：<?php echo(date('Y年m月d日')); ?></div>
	</div>
</div>

<script type="text/javascript">
	$(function(){
		var h = location.href;
		if(h.indexOf('Chaoshi')>-1){
			$('#n2').addClass('nav_cur');
		}else if(h.indexOf('dingzhi')>-1){
			$('#n3').addClass('nav_cur');
		}else if(h.indexOf('favor')>-1){
			$('#n4').addClass('nav_cur');
			$('#left_3').addClass('nav_cur');
		}else if(h.indexOf('contact')>-1){
			$('#n5').addClass('nav_cur');
		}else if(h.indexOf('info')>-1){
			$('#left_1').addClass('nav_cur');
		}else {
			$('#n1').addClass('nav_cur');
		}
	});
</script>





	<div class="m_auto">

		<div class="bar">

			<span >会员中心</span>

		</div>



		<div class="clearfix ">

			<ul class="center_ul fl al_ct f_16 mt_10">
	<li><a href="<?php echo U('Center/info');?>" class="dis_b clr_f" id="left_1">基本资料</a></li>
	<li><a href="<?php echo U('Center/dingzhi');?>" class="dis_b clr_f">个性需求</a></li>
	<li><a href="<?php echo U('Center/favor');?>" class="dis_b clr_f"  id="left_3">我的收藏</a></li>
</ul>



			<div class="fr border1    f_16 clr_3 mt_10 center_right" style="min-height: 500px;">

				<a href="#." id="excelClick">

					<img src="/Public/images/excel.png" class="dis_ib ver_mid" />

					<span class="ver_mid" style="color:#217346;">导出收藏报价</span>

				</a>

				<?php if(is_array($lists)): foreach($lists as $key=>$v): ?><div class="border1 bg_gr pad_10 f_14 clr_3 clearfix mt_10 pos_rela">

						<div class="fl" style="width:700px;" >

							<div>

								<!-- <div class="dis_ib cs1 "><div class="l1  dis_ib">5星</div><div class="l2  dis_ib">卖家</div></div> -->

								<a href="<?php echo U('Chaoshi/view',['id'=>$v['res']['id']]);?>" target="_blank" class="clr_b f_wei"><?php echo ($v["res"]["pihao_kunkao"]); ?></a>

								<span class="ml_10"><?php echo ($v["res"]["leixing"]); ?></span>

								<span class="ml_10"><?php echo ($v["res"]["baoshu"]); ?>件</span>

								<span class="clr_9">加工厂 <span class="clr_3"><?php echo ($v["res"]["jiagongchang"]); ?></span></span>

							</div>



							<div class="mt_5 lh_30">

								<span class="clr_9">颜色 <span class="clr_3"><?php echo ($v["res"]["yanseji_pinji"]); ?></span></span>
                                </div>
                                <div  class="mt_5 lh_30">
                                	<span class="clr_9">长度 <span class="clr_3"><?php echo ($v["res"]["changdu"]); ?></span></span>
	                           <span class="clr_9">强力 <span class="clr_3"><?php echo ($v["res"]["qiangli"]); ?></span></span>
								<span class="clr_9">马值 <span class="clr_3"><?php echo ($v["res"]["mazhi"]); ?></span></span>


								<span class="clr_9">含杂<span class="clr_3"><?php echo ($v["res"]["hanzalv"]); ?></span></span>

								<span class="clr_9">回潮 <span class="clr_3"><?php echo ($v["res"]["huichaolv"]); ?></span></span>

								<span class="clr_9">整齐度<span class="clr_3"><?php echo ($v["res"]["zhengqidu"]); ?></span></span><br />



								

							</div>

							<div class="mt_5">

								<span class="clr_9">仓储名称 <span class="clr_3"><?php echo ($v["res"]["cangchumingcheng"]); ?></span></span>

								<span class="clr_9" style="margin:0 15px;">

									<span class="clr_3"><?php echo ($v["diqu_text"]); ?></span>

								</span>

								<span class="clr_9">更新时间： <span class="clr_3"><?php echo (date("Y/m/d H:i", $v["res"]["add_time"])); ?></span></span>

							</div>

							<!-- <div class="mt_10"><textarea  class="border1 pad_10 memo_text" myid="<?php echo ($v["id"]); ?>" placeholder="备注" style="width:600px;height:40px;"><?php echo ($v["memo"]); ?></textarea></div> -->

						</div>



						<div class="fr clearfix f_14 al_ct " style="width:250px;">

							<div class="clr_9 ">参考合约： <span><?php echo ($v["res"]["dianjiaheyue"]); ?></span></div>

							<div class="clr_9 ">基差： <span class="clr_3 f_20"><?php echo ($v["res"]["jicha"]); ?></span></div>

							<a href="#." class="f_16 zy_box clr_ora dis_ib mt_5 del_favor" myid="<?php echo ($v["id"]); ?>" >

								<span class="ver_mid">取消收藏</span>

							</a>

						</div>

					</div><?php endforeach; endif; ?>



			</div>



		</div>

	</div>

<script type="text/javascript">



$('#excelClick').click(function(){

	$.getJSON('<?php echo U("toFavorExcel");?>', {}, function(d){

	 	console.log(d)

	 	if(d.code==1){

	 		location.href=d.url;

	 	}else{

	 		alert(d.msg);

	 	}

	 });

});



$('.memo_text').blur(function(){

	if(confirm('确认保存？')){

		$.post('<?php echo U("sMemo");?>', {id:$(this).attr('myid'),memo:$(this).val()}, function(d){

			alert(d.msg);

			history.go(0);

		},'json');

	}

});

$('.del_favor').click(function(){

	if(confirm('确认取消收藏？')){

		$.getJSON('<?php echo U("delFavor");?>', {id:$(this).attr('myid')}, function(d){

			// alert(d.msg);

			if(d.code==1){

				history.go(0);

			}

		});

	}

	

});

</script>



	<div class="footer mt_30 min_w">

		<div class="f_14 clr_9 al_ct padt_10 lh_28">

版权所有：青岛三匹马实业有限公司&nbsp;&nbsp;&nbsp;&nbsp;地址：青岛市黄岛区江山南路450号（富安国际大厦）&nbsp;&nbsp;&nbsp;&nbsp;电话：17866631857<br/>



	  Copyright &copy; 2021 鲁ICP备2021017032号-1



 </div>
 
  
<div class="side">
	<ul>
		
		<li class="sideewm"><i class="bgs3"></i>官方微信
			<div class="ewBox son"></div>
		</li>
		<li class="sideetel"><i class="bgs4"></i>联系电话
			<div class="telBox son">
			
				<dd class="bgs2"><span>手机</span>19853227218</dd>
			</div>
		</li>
	
		<li class="sidetop" onClick="goTop()"><i class="bgs6"></i>返回顶部</li>
	</ul>
</div>

<script src="/Public/js/jquery-1.11.0.min.js" type="text/javascript" charset="utf-8"></script>
<script>
	function goTop() {
		$('html,body').animate({
			scrollTop: 0
		}, 500)
	}
</script>

 <style>
 .side{position:fixed;width:60px;right:0;top:60%;margin-top:-200px;z-index:100;border:1px solid #e0e0e0;background:#fff;border-bottom:0}
.side ul li{width:60px;height:70px;float:left;position:relative;border-bottom:1px solid #e0e0e0;color:#333;font-size:14px;line-height:38px;text-align:center;transition:all .3s;cursor:pointer}
.side ul li:hover{background:#f67524;color:#fff}
.side ul li:hover a{color:#fff}
.side ul li i{height:25px;margin-bottom:1px;display:block;overflow:hidden;background-repeat:no-repeat;background-position:center center;background-size:auto 25px;margin-top:14px;transition:all .3s}
.side ul li i.bgs1{background-image:url(/Public/images/right_pic5.png)}
.side ul li i.bgs2{background-image:url(/Public/images/right_pic7.png)}
.side ul li i.bgs3{background-image:url(/Public/images/right_pic2.png)}
.side ul li i.bgs4{background-image:url(/Public/images/right_pic1.png)}
.side ul li i.bgs5{background-image:url(/Public/images/right_pic3.png)}
.side ul li i.bgs6{background-image:url(/Public/images/right_pic6_on.png)}
.side ul li:hover i.bgs1{background-image:url(/Public/images/right_pic5_on.png)}
.side ul li:hover i.bgs2{background-image:url(/Public/images/right_pic7_on.png)}
.side ul li:hover i.bgs3{background-image:url(/Public/images/right_pic2_on.png)}
.side ul li:hover i.bgs4{background-image:url(/Public/images/right_pic1_on.png)}
.side ul li:hover i.bgs5{background-image:url(/Public/images/right_pic3_on.png)}
.side ul li .sidebox{position:absolute;width:78px;height:78px;top:0;right:0;transition:all .3s;overflow:hidden}
.side ul li.sidetop{background:#f67524;color:#fff}
.side ul li.sidetop:hover{opacity:.8;filter:Alpha(opacity=80)}
.side ul li.sideewm .ewBox.son{width:238px;display:none;color:#363636;text-align:center;padding-top:212px;position:absolute;left:-240px;top:0;background-image:url(/Public/images/leftewm.png);background-repeat:no-repeat;background-position:center center;border:1px solid #e0e0e0}
.side ul li.sideetel .telBox.son{width:240px;height:214px;display:none;color:#fff;text-align:left;position:absolute;left:-240px;top:-72px;background:#f67524}
.side ul li.sideetel .telBox dd{display:block;height:118.5px;overflow:hidden;padding-left:82px;line-height:24px;font-size:18px}
.side ul li.sideetel .telBox dd span{display:block;line-height:28px;height:28px;overflow:hidden;margin-top:32px;font-size:18px}
.side ul li.sideetel .telBox dd.bgs1{background:url(/Public/images/right_pic8.png) 28px center no-repeat;background-color:#e96410}
.side ul li.sideetel .telBox dd.bgs2{background:url(/Public/images/right_pic9.png) 28px center no-repeat}
.side ul li:hover .son{display:block!important;animation:fadein 1s}
@keyframes fadein{from{opacity:0}
to{opacity:1}
}
</style>

	</div>





</body>





</html>
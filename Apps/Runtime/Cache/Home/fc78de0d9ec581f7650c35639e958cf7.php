<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>
<html>
<head>
<title>青岛三匹马实业</title>
<meta name="keywords" content="青岛三匹马实业" />
<meta name="description" content="青岛三匹马实业" />
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

<script type="text/javascript" src="/Public/js/jquery-1.11.1.min.js"></script>

<script type="text/javascript" src="/Public/js/swiper.min.js"></script>

<link rel="stylesheet" type="text/css" href="/Public/css/css.css" />

<link rel="stylesheet" type="text/css" href="/Public/css/swiper.min.css">
<link rel="icon" type="image/x-icon" href="/Public/images/favicon.ico">
<script type="text/javascript" src="/Public/js/ion.rangeSlider.min.js"></script>
<link rel="stylesheet" type="text/css" href="/Public/css/ion.rangeSlider.min.css" />
<script type="text/javascript">
$(function(){
	function isIE() {
	    if (!!window.ActiveXObject || "ActiveXObject" in window){
	         return true;
	     }else{
	         return false;
	     }
	 }   

	 if(isIE()){
	 	$('.hang2').css({'margin-top':'10px'});
	 	$('.length').css({'top':'0'});
	 }else{
	 	$('.hang1').css({'margin-top':'-20px'});

	 }
});
</script>
</head>

<body>


		<div class="border1 pad_20 bg_f mt_10">
			<ul class="mian_ul clearfix al_ct f_18">
				<?php if(is_array($chandi)): foreach($chandi as $k=>$v): if($def == $v['id']): ?><li><a href="#." class="clr_3 mian_cur"><?php echo ($v["name"]); ?></a></li>
					<?php else: ?>
						<li><a href="#." class="clr_3 mian_norm"><?php echo ($v["name"]); ?></a></li><?php endif; endforeach; endif; ?>
			</ul>

			<ul class="chandi_ul f_14 mt_20 fl" style="line-height:30px;margin-left:5px;width:370px;">
				<li  style="display: inline-block;" class="ver_top">产　地：</li>
				<li  style="display: inline-block;"  >
					<div id="chandi_click">
						<?php if(is_array($chandi_default)): foreach($chandi_default as $key=>$v): if(strpos($_GET['cd'], '|'.$v['id'].'-|') !== false): ?><a href="#." class="clr_3 mr_10 xuan_cur" myid="<?php echo ($v["id"]); ?>"><?php echo ($v["name"]); ?></a>
							<?php else: ?>
								<a href="#." class="clr_3 mr_10 " myid="<?php echo ($v["id"]); ?>"><?php echo ($v["name"]); ?></a><?php endif; endforeach; endif; ?>
					</div>
				</li>
			</ul>

			<ul class="chandi_ul f_14 mt_20 fl "  id="leixing_ul" style="line-height:30px; margin-left:5px;width:375px;">
				<li>类　型：</li>
				<?php if(is_array($xls_leixing)): foreach($xls_leixing as $key=>$v): if(strpos($_GET['leixing'], '|'.$v.'|') !== false): ?><li><a href="#." class="clr_3 xuan_cur"><?php echo ($v); ?></a></li>
					<?php else: ?>
						<li><a href="#." class="clr_3"><?php echo ($v); ?></a></li><?php endif; endforeach; endif; ?>
			</ul>

			<ul class="chandi_ul f_14 mt_20 fl " id="niandu_ul" style="line-height:30px; margin-left:5px; width:330px;">
				<li>年　度：</li>
				<?php if(is_array($xls_niandu)): foreach($xls_niandu as $key=>$v): if(strpos($_GET['pihao_kunkao'], '|'.$v.'|') !== false): ?><li><a href="#." class="clr_3 xuan_cur"><?php echo ($v); ?></a></li>
					<?php else: ?>
						<li><a href="#." class="clr_3"><?php echo ($v); ?></a></li><?php endif; endforeach; endif; ?>
			</ul>

			<div class="cls"></div>
			<div id="child_wraper">
				<?php if(is_array($chandi_default)): foreach($chandi_default as $key=>$v): ?><span id="child_<?php echo ($v["id"]); ?>">
						<?php if($_GET['cd2'] != '' and $_GET['cd2'] != '|'): if(is_array($cd2[$v['id']])): foreach($cd2[$v['id']] as $key=>$v2): if(strpos($_GET['cd2'], '|'.$v['id'].'-'.$v2['id'].'-|') !== false): ?><a href="#." class="clr_3 mr_10 xuan_cur f_14" pid="<?php echo ($v["id"]); ?>" myid="<?php echo ($v2["id"]); ?>"><?php echo ($v2["name"]); ?></a>
								<?php else: ?>
									<a href="#." class="clr_3 mr_10 f_14" pid="<?php echo ($v["id"]); ?>" myid="<?php echo ($v2["id"]); ?>"><?php echo ($v2["name"]); ?></a><?php endif; endforeach; endif; endif; ?>
					</span><?php endforeach; endif; ?>
			</div>

			<div id="grandson_wraper" class="mt_10">
				<?php if(is_array($xx)): foreach($xx as $k=>$v): if(is_array($v)): foreach($v as $key=>$v2): ?><span id="grandson_<?php echo ($v2["id"]); ?>" gpid="<?php echo ($k); ?>" pid="<?php echo ($v2["id"]); ?>">
							<?php if($_GET['cd3'] != '' and $_GET['cd3'] != '|' and strpos($_GET['cd2'], '|'.$k.'-'.$v2['id'].'-|') !== false): if(is_array($v2["children"])): foreach($v2["children"] as $key=>$v3): if(strpos($_GET['cd3'], '|'.$k.'-'.$v3['pid'].'-'.$v3['id'].'-|') !== false): ?><a href="#." gpid="<?php echo ($k); ?>" pid="<?php echo ($v3["pid"]); ?>" myid="<?php echo ($v3["id"]); ?>" class="mr_10 clr_3 f_14 xuan_cur"><?php echo ($v3["name"]); ?></a>
									<?php else: ?>
										<a href="#." gpid="<?php echo ($k); ?>" pid="<?php echo ($v3["pid"]); ?>" myid="<?php echo ($v3["id"]); ?>" class="mr_10 clr_3 f_14"><?php echo ($v3["name"]); ?></a><?php endif; endforeach; endif; endif; ?>
						</span><?php endforeach; endif; endforeach; endif; ?>
			</div>




			<script type="text/javascript">

				//点击子产地
				$('#child_wraper').on('click', 'a', function(){
					var that  = $(this);

					if(!that.hasClass('xuan_cur')){
						that.addClass('xuan_cur');
					}else{
						that.removeClass('xuan_cur');
					}
				});

				//点击父产地
				$('#chandi_click').on('click', 'a', function(){
					var that  = $(this);
					if(!that.hasClass('xuan_cur')){
						$.getJSON('<?php echo U("Tools/getCat");?>', {id:that.attr('myid')}, function(d){
							// console.log(d)
							$.each(d, function(k,v){
								$('#child_'+that.attr('myid')).append('<a href="#." pid="'+v.pid+'" myid="'+v.id+'" class="mr_10 clr_3 f_14">'+v.name+'</a>');
								$('#grandson_wraper').append('<span id="grandson_'+v.id+'" gpid="'+v.pid+'"></span>')
							});
							that.addClass('xuan_cur');
						});
					}else{
						that.removeClass('xuan_cur');
					}

					$('#child_'+that.attr('myid')+' a[pid="'+that.attr('myid')+'"]').remove();
					$('#grandson_wraper a[gpid="'+that.attr('myid')+'"]').remove();
					//删除对应二级的占位
					$('#grandson_wraper span[gpid='+that.attr('myid')+']').remove();
				});
			</script>


			<script type="text/javascript">
				var leixing_str= '<?php echo ($_GET["leixing"]); ?>' == '' ? '|' : '<?php echo ($_GET["leixing"]); ?>' ;
				// console.log(leixing_str)
				$('#leixing_ul li a').click(function(){
					if(!$(this).hasClass('xuan_cur')){
						$(this).addClass('xuan_cur');
						if(leixing_str.indexOf('|'+$(this).text()+'|') ==-1){
							leixing_str += $(this).text()+'|';
						}
					}else{
						$(this).removeClass('xuan_cur');
						leixing_str = leixing_str.replace('|'+$(this).text()+'|', '|');
					}

					$('input[name="leixing"]').val(leixing_str);
				});
			</script>


			<script type="text/javascript">
				var niandu_str=  '<?php echo ($_GET["pihao_kunkao"]); ?>' == '' ? '|' : '<?php echo ($_GET["pihao_kunkao"]); ?>' ;
				$('#niandu_ul li a').click(function(){
					if(!$(this).hasClass('xuan_cur')){
						$(this).addClass('xuan_cur');
						if(niandu_str.indexOf('|'+$(this).text()+'|') ==-1){
							niandu_str += $(this).text()+'|';
						}
					}else{
						$(this).removeClass('xuan_cur');
						niandu_str = niandu_str.replace('|'+$(this).text()+'|', '|');
					}

					 $('input[name="pihao_kunkao"]').val(niandu_str);
				});
			</script>

			<div class="cls"></div>

			<ul class="chandi_ul f_14  fl hang1" style="width:380px;">
				<li class="length">长　度</li>
				<li style="width:240px;"><input type="text" class="changdu_range" name="my_range" value=""   /></li>
			</ul>
			<script>
				$(".changdu_range").ionRangeSlider({
					type: "double",
					skin:"round",
						type: "double",
						min: 25,
						max: 32,
						from: '<?php echo ($_GET["changdu_from"]); ?>' == '' ? 25 : '<?php echo ($_GET["changdu_from"]); ?>',
						to: '<?php echo ($_GET["changdu_to"]); ?>' == '' ? 32 : '<?php echo ($_GET["changdu_to"]); ?>',
						step:0.1, 
						onFinish: function (data) { //拖动结束回调
							 // console.log(data)
							 $('input[name="changdu_from"]').val(data.from);
							 $('input[name="changdu_to"]').val(data.to);
						}
		 
				});
			</script>


			
			<ul class="chandi_ul f_14 fl hang1" style="width:380px;">
				<li  class="length">强　力</li>
				<li style="width:240px;"><input type="text" class="qiangli_range" name="my_range" value=""   /></li>
			</ul>
			<script>
				$(".qiangli_range").ionRangeSlider({
					type: "double",
					skin:"round",
						type: "double",
						min: 25,
						max: 32,
						from: '<?php echo ($_GET["qiangli_from"]); ?>' == '' ? 25 : '<?php echo ($_GET["qiangli_from"]); ?>',
						to: '<?php echo ($_GET["qiangli_to"]); ?>' == '' ? 32 : '<?php echo ($_GET["qiangli_to"]); ?>',
						step:0.1, 
						onFinish: function (data) { //拖动结束回调
							 // console.log(data)
								$('input[name="qiangli_from"]').val(data.from);
							 $('input[name="qiangli_to"]').val(data.to);
						}

		 
				});
			</script>

			<ul class="chandi_ul f_14  fl hang1"  style="width:380px;">
				<li class="length">马　值</li>
				<li style="width:240px;"><input type="text" class="mazhi_range" name="my_range" value=""   /></li>
			</ul>
			<script>
				$(".mazhi_range").ionRangeSlider({
					type: "double",
					skin:"round",
						type: "double",
						min: 2.5,
						max: 5.5,
						from: '<?php echo ($_GET["mazhi_from"]); ?>' == '' ? 2.5 : '<?php echo ($_GET["mazhi_from"]); ?>',
						to: '<?php echo ($_GET["mazhi_to"]); ?>' == '' ? 5.5 : '<?php echo ($_GET["mazhi_to"]); ?>',
						step:0.1, 
						onFinish: function (data) { //拖动结束回调
							 // console.log(data)
							 $('input[name="mazhi_from"]').val(data.from);
							 $('input[name="mazhi_to"]').val(data.to);
						}
		 
				});
			</script>



			<div class="cls"></div>

			<ul class="chandi_ul f_14  fl hang2" style="width:380px;">
				<li class="length">回　潮</li>
				<li style="width:240px;"><input type="text" class="huichao_range" name="my_range" value=""   /></li>
			</ul>
			<script>
				$(".huichao_range").ionRangeSlider({
					type: "double",
					skin:"round",
						type: "double",
						min: 0,
						max: 10,
						from: '<?php echo ($_GET["huichaolv_from"]); ?>' == '' ? 0 : '<?php echo ($_GET["huichaolv_from"]); ?>',
						to: '<?php echo ($_GET["huichaolv_to"]); ?>' == '' ? 10 : '<?php echo ($_GET["huichaolv_to"]); ?>',
						step:0.1, 
						onFinish: function (data) { //拖动结束回调
							 // console.log(data)
							 $('input[name="huichaolv_from"]').val(data.from);
							 $('input[name="huichaolv_to"]').val(data.to);
						}
		 
				});
			</script>

			<ul class="chandi_ul f_14  fl hang2"  style="width:380px;">
				<li class="length">整齐度</li>
				<li style="width:240px;"><input type="text" class="zhengqidu_range" name="my_range" value=""   /></li>
			</ul>
			<script>
				$(".zhengqidu_range").ionRangeSlider({
					type: "double",
					skin:"round",
						type: "double",
						min: 77,
						max: 90,
						from: '<?php echo ($_GET["zhengqidu_from"]); ?>' == '' ? 77 : '<?php echo ($_GET["zhengqidu_from"]); ?>',
						to: '<?php echo ($_GET["zhengqidu_to"]); ?>' == '' ? 90 : '<?php echo ($_GET["zhengqidu_to"]); ?>',
						step:0.1, 
						onFinish: function (data) { //拖动结束回调
							 // console.log(data)
							 $('input[name="zhengqidu_from"]').val(data.from);
							 $('input[name="zhengqidu_to"]').val(data.to);
						}
		 
				});
			</script>

			<ul class="chandi_ul f_14  fl hang2" style="width:380px;">
				<li class="length">含　杂</li>
				<li style="width:240px;"><input type="text" class="hanza_range" name="my_range" value=""   /></li>
			</ul>
			<script>
				$(".hanza_range").ionRangeSlider({
					type: "double",
					skin:"round",
						type: "double",
						min: 0,
						max: 6,
						from: '<?php echo ($_GET["hanzalv_from"]); ?>' == '' ? 0 : '<?php echo ($_GET["hanzalv_from"]); ?>',
						to: '<?php echo ($_GET["hanzalv_to"]); ?>' == '' ? 6 : '<?php echo ($_GET["hanzalv_to"]); ?>',
						step:0.1, 
						onFinish: function (data) { //拖动结束回调
							 // console.log(data)
								$('input[name="hanzalv_from"]').val(data.from);
							 $('input[name="hanzalv_to"]').val(data.to);
						}
		 
				});
			</script>


			<div class="cls"></div>


			<div class="bottom">
				<ul class="chandi_ul f_14  fl hang2" style="width:380px;">
					<li class="green length">白棉1/2/3级</li>
					<li style="width:240px;">
						<input type="text" class="bm123_range" name="bm123_range" value=""   />
					</li>
				</ul>
				<script>
					$(".bm123_range").ionRangeSlider({
						type: "double",
						skin:"round",
							type: "double",
							min: 0,
							max: 100,
							from: '<?php echo ($_GET["bm123_from"]); ?>' == '' ? 0 : '<?php echo ($_GET["bm123_from"]); ?>',
							to:'<?php echo ($_GET["bm123_to"]); ?>' == '' ? 100 : '<?php echo ($_GET["bm123_to"]); ?>',
							step:0.1, 
							onFinish: function (data) { //拖动结束回调
								 // console.log(data)
								 $('input[name="bm123_from"]').val(data.from);
								 $('input[name="bm123_to"]').val(data.to);
							}
			 
					});
				</script>
				<ul class="chandi_ul f_14  fl hang2" style="width:380px;">
					<li class="green length">白棉4/5级</li>
					<li style="width:240px;"><input type="text" class="bm45_range" name="bm45_range" value=""   /></li>
				</ul>
				<script>
					$(".bm45_range").ionRangeSlider({
						type: "double",
						skin:"round",
							type: "double",
							min: 0,
							max: 100,
							from: '<?php echo ($_GET["bm45_from"]); ?>' == '' ? 0 : '<?php echo ($_GET["bm45_from"]); ?>',
							to: '<?php echo ($_GET["bm45_to"]); ?>' == '' ? 100 : '<?php echo ($_GET["bm45_to"]); ?>',
							step:0.1, 
							onFinish: function (data) { //拖动结束回调
								 // console.log(data)
									$('input[name="bm45_from"]').val(data.from);
								 $('input[name="bm45_to"]').val(data.to);
							}

			 
					});
				</script>
				<ul class="chandi_ul f_14  fl hang2"  style="width:380px;">
					<li class="green length">淡点污1/2/3级</li>
					<li style="width:240px;">
						<input type="text" class="ddw123_range" name="ddw123_range" value=""   />
					</li>
				</ul>
				<script>
					$(".ddw123_range").ionRangeSlider({
						type: "double",
						skin:"round",
							type: "double",
							min: 0,
							max: 100,
							from: '<?php echo ($_GET["ddw123_from"]); ?>' == '' ? 0 : '<?php echo ($_GET["ddw123_from"]); ?>',
							to: '<?php echo ($_GET["ddw123_to"]); ?>' == '' ? 100 : '<?php echo ($_GET["ddw123_to"]); ?>',
							step:0.1, 
							onFinish: function (data) { //拖动结束回调
								 // console.log(data)
								 $('input[name="ddw123_from"]').val(data.from);
								 $('input[name="ddw123_to"]').val(data.to);
							}
			 
					});
				</script>

				<div class="cls"></div>

				<ul class="chandi_ul f_14  fl hang2" style="width:380px;">
					<li class="green length">淡黄染1/2/3级</li>
					<li style="width:240px;">
						<input type="text" class="dhr123_range" name="dhr123_range" value=""   />
					</li>
				</ul>
				<script>
					$(".dhr123_range").ionRangeSlider({
						type: "double",
						skin:"round",
							type: "double",
							min: 0,
							max: 100,
							from: '<?php echo ($_GET["dhr123_from"]); ?>' == '' ? 0 : '<?php echo ($_GET["dhr123_from"]); ?>',
							to: '<?php echo ($_GET["dhr123_to"]); ?>' == '' ? 100 : '<?php echo ($_GET["dhr123_to"]); ?>',
							step:0.1, 
							onFinish: function (data) { //拖动结束回调
								 // console.log(data)
								 $('input[name="dhr123_from"]').val(data.from);
								 $('input[name="dhr123_to"]').val(data.to);
							}
			 
					});
				</script>
				<ul class="chandi_ul f_14  fl hang2" style="width:380px;">
					<li class="green length">黄染1/2级</li>
					
					<li style="width:240px;"><input type="text" class="hr12_range" name="hr12_range" value=""   /></li>
				</ul>
				<script>
					$(".hr12_range").ionRangeSlider({
						type: "double",
						skin:"round",
							type: "double",
							min: 0,
							max: 100,
							from: '<?php echo ($_GET["hr12_from"]); ?>' == '' ? 0 : '<?php echo ($_GET["hr12_from"]); ?>',
							to: '<?php echo ($_GET["hr12_to"]); ?>' == '' ? 100 : '<?php echo ($_GET["hr12_to"]); ?>',
							step:0.1, 
							onFinish: function (data) { //拖动结束回调
								 // console.log(data)
									$('input[name="hr12_from"]').val(data.from);
								 $('input[name="hr12_to"]').val(data.to);
							}
			 
					});
				</script>
				
				<div class="cls"></div>
			</div>


			<ul class="chandi_ul f_14 mt_30 " id="jiaohuodi_ul" style="width:760px;">
				<li>交货地：</li>
				<li >
					<div id="jhd_click">
						<?php if(is_array($jiaohuodi)): foreach($jiaohuodi as $k=>$v): if(strpos($_GET['jhd'], '|'.$v['id'].'-|') !== false): ?><a href="#." class="clr_3 mr_10 xuan_cur" myid="<?php echo ($v["id"]); ?>"><?php echo ($v["name"]); ?></a>
							<?php else: ?>
								<a href="#." class="clr_3 mr_10 " myid="<?php echo ($v["id"]); ?>"><?php echo ($v["name"]); ?></a><?php endif; endforeach; endif; ?>
					</div>
				</li>
			</ul>
			

			<div id="jhd_wraper" class="mt_10">
				<?php if(is_array($jiaohuodi)): foreach($jiaohuodi as $key=>$v): ?><span id="jhd_<?php echo ($v["id"]); ?>">
						<?php if($_GET['jhd2'] != '' and $_GET['jhd2'] != '|'): if(is_array($jhd2[$v['id']])): foreach($jhd2[$v['id']] as $key=>$v2): if(strpos($_GET['jhd2'], '|'.$v['id'].'-'.$v2['id'].'-|') !== false): ?><a href="#." class="clr_3 mr_10 xuan_cur f_14" pid="<?php echo ($v["id"]); ?>" myid="<?php echo ($v2["id"]); ?>"><?php echo ($v2["name"]); ?></a>
								<?php else: ?>
									<a href="#." class="clr_3 mr_10 f_14" pid="<?php echo ($v["id"]); ?>" myid="<?php echo ($v2["id"]); ?>"><?php echo ($v2["name"]); ?></a><?php endif; endforeach; endif; endif; ?>
					</span><?php endforeach; endif; ?>
			</div>


			<script type="text/javascript">
			//点击父交货地
				$('#jhd_click').on('click', 'a', function(){
					var that  = $(this);
					if(!that.hasClass('xuan_cur')){
						$.getJSON('<?php echo U("Tools/getJhd");?>', {id:that.attr('myid')}, function(d){
							// console.log(d)
							$.each(d, function(k,v){
								$('#jhd_'+that.attr('myid')).append('<a href="#." pid="'+v.pid+'" myid="'+v.id+'" class="mr_10 clr_3 f_14">'+v.name+'</a>');
							});
							that.addClass('xuan_cur');
						});
					}else{
						that.removeClass('xuan_cur');
					}

					$('#jhd_'+that.attr('myid')+' a[pid="'+that.attr('myid')+'"]').remove();
				});


				//点击子交货地
				$('#jhd_wraper').on('click', 'a', function(){
					var that  = $(this);
					if(!that.hasClass('xuan_cur')){
						that.addClass('xuan_cur');
					}else{
						that.removeClass('xuan_cur');
					}
				});
			</script>

			<form action="<?php echo U('rDingzhi');?>" id="fm1" method="post">
				<input id="act" name="act" value="" type="hidden" />
				<div class="dis_n">
					<input name="def" value="<?php echo ($def); ?>" />
					<!-- 父产地 -->
					<input name="cd" value="" />
					<!-- 子产地 -->
					<input name="cd2" value="" />
					<!-- 孙产地 -->
					<input name="cd3" value="" />
					
					<!-- 父交货地-->
					<input name="jhd" value="" placeholder="jhd" />
					<!-- 子交货地 -->
					<input name="jhd2" value=""  placeholder="jhd2"/>

					<textarea id="url" name="url"></textarea>
					<input name="leixing" value="<?php echo ($_GET['leixing']); ?>" />
					<input name="pihao_kunkao" value="<?php echo ($_GET['pihao_kunkao']); ?>" />
					<input name="changdu_from" value="<?php echo ($_GET['changdu_from'] == '' ? 25 : $_GET['changdu_from']); ?>" />
					<input name="changdu_to" value="<?php echo ($_GET['changdu_to'] == '' ? 32 : $_GET['changdu_to']); ?>"/>
					<input name="qiangli_from" value="<?php echo ($_GET['qiangli_from'] == '' ? 25 : $_GET['qiangli_from']); ?>"/>
					<input name="qiangli_to" value="<?php echo ($_GET['qiangli_to'] == '' ? 32 : $_GET['qiangli_to']); ?>"/>
					<input name="mazhi_from" value="<?php if($_GET['mazhi_from'] == ''){echo (2.5);}else{echo ($_GET['mazhi_from']);} ?>"/>
					<input name="mazhi_to" value="<?php if($_GET['mazhi_to'] == ''){echo (5.5);}else{echo ($_GET['mazhi_to']);} ?>"/>
					<input name="huichaolv_from" value="<?php echo ($_GET['huichaolv_from'] == '' ? 0 : $_GET['huichaolv_from']); ?>"/>
					<input name="huichaolv_to" value="<?php echo ($_GET['huichaolv_to'] == '' ? 10 : $_GET['huichaolv_to']); ?>"/>
					<input name="hanzalv_from" value="<?php echo ($_GET['hanzalv_from'] == '' ? 0 : $_GET['hanzalv_from']); ?>"/>
					<input name="hanzalv_to" value="<?php echo ($_GET['hanzalv_to'] == '' ? 5 : $_GET['hanzalv_to']); ?>"/>
					<input name="zhengqidu_from" value="<?php echo ($_GET['zhengqidu_from'] == '' ? 77 : $_GET['zhengqidu_from']); ?>"/>
					<input name="zhengqidu_to" value="<?php echo ($_GET['zhengqidu_to'] == '' ? 90 : $_GET['zhengqidu_to']); ?>"/>
					<input name="bm123_from" value="<?php echo ($_GET['bm123_from'] == '' ? 0 : $_GET['bm123_from']); ?>"/>
					<input name="bm123_to" value="<?php echo ($_GET['bm123_to'] == '' ? 100 : $_GET['bm123_to']); ?>"/>
					<input name="bm45_from" value="<?php echo ($_GET['bm45_from'] == '' ? 0 : $_GET['bm45_from']); ?>"/>
					<input name="bm45_to" value="<?php echo ($_GET['bm45_to'] == '' ? 100 : $_GET['bm45_to']); ?>"/>
					<input name="ddw123_from" value="<?php echo ($_GET['ddw123_from'] == '' ? 0 : $_GET['ddw123_from']); ?>"/>
					<input name="ddw123_to" value="<?php echo ($_GET['ddw123_to'] == '' ? 100 : $_GET['ddw123_to']); ?>"/>
					<input name="dhr123_from" value="<?php echo ($_GET['dhr123_from'] == '' ? 0 : $_GET['dhr123_from']); ?>"/>
					<input name="dhr123_to" value="<?php echo ($_GET['dhr123_to'] == '' ? 100 : $_GET['dhr123_to']); ?>"/>
					<input name="hr12_from" value="<?php echo ($_GET['hr12_from'] == '' ? 0 : $_GET['hr12_from']); ?>"/>
					<input name="hr12_to" value="<?php echo ($_GET['hr12_to'] == '' ? 100 : $_GET['hr12_to']); ?>"/>
				</div>
				<ul class="chandi_ul f_14 mt_20">
					<li>
						交货仓库：<input class="border1 f_14 clr_9 pad_5" placeholder="请输入仓库名称"  
							value="<?php echo ($_GET['cangchumingcheng']); ?>" name="cangchumingcheng" style="width:350px;" /> 
						轧花厂：<input class="border1 f_14 clr_9 pad_5" placeholder="请输入轧花厂名称或编号" 
							value="<?php echo ($_GET['jiagongchang']); ?>" name="jiagongchang"  style="width:350px;" /> 
						<input type="button" id="tijiaoClick" value="✓ 确认筛选　" class="f_14 clr_f bg_lan border0 pad_5" />
						<!-- <a href="#." id="add_dingzhi" style=" font-weight: 700;
						color: #F1830E;
						padding-left: 10px;
						font-size: 15px;">+添加到个性需求</a> -->
						<!-- <a href="#" class="clr_ora f_wei">+添加到需求定制</a> -->
					</li>
				</ul>

				<div class="dis_n">
					<input name="hot" value="<?php echo ($_GET['hot']); ?>" />
					<input name="heyue" value="<?php echo ($_GET['heyue']); ?>" />
					<input name="zuiyou" value="<?php echo ($_GET['zuiyou']); ?>" />
					<input name="xiangtong"  value="<?php echo ($_GET['xiangtong']); ?>" />
					<input name="dan"  value="<?php echo ($_GET['dan']); ?>" />
					<input name="jicha"  value="<?php echo ($_GET['jicha']); ?>" />
				</div>
			</form>


			<script type="text/javascript">
				$('#add_dingzhi').click(function(){
					setForm();
					$('#url').val($('#fm1').serialize());
					$.post('<?php echo U("rDingzhi");?>',$('#fm1').serialize(), function(d){
						alert(d.msg);
						if(d.code==1){
							location.href="<?php echo U('Center/dingzhi');?>";
						}
					},'json');
				});

				function setForm(){
					//提交表单，而不是导出excel
					$('#act').val('');

					//所选的父产地
					var cd = '|';
					$('#chandi_click a').each(function(k, v){
						if($(this).hasClass('xuan_cur')){
							// cd += $(this).text() + '|';
							// console.log('v----'+$(this).attr('myid'));
							cd += $(this).attr('myid') + '-|';
						}
					});

					$('input[name="cd"]').val(cd);	

					
					//所选的子产地
					var cd2 = '|';
					$('#child_wraper a').each(function(k, v){
						if($(this).hasClass('xuan_cur')){
							// cd2 += $(this).text() + '|';
							// console.log('v----'+$(this).attr('myid'));
							cd2 += $(this).attr('pid')+'-'+$(this).attr('myid') + '-|';
						}
						
					});

					$('input[name="cd2"]').val(cd2);

					//所选的孙产地
					var cd3 = '|';
					$('#grandson_wraper a').each(function(k, v){
						if($(this).hasClass('xuan_cur')){
							// cd3+= $(this).text() + '|';
							// console.log('v----'+$(this).attr('myid'));
							cd3+= $(this).attr('gpid')+'-'+$(this).attr('pid')+'-'+$(this).attr('myid')+ '-|';
						}
						
					});

					$('input[name="cd3"]').val(cd3);


					//所选的父交货地
					var jhd = '|';
					$('#jhd_click a').each(function(k, v){
						if($(this).hasClass('xuan_cur')){
							// cd += $(this).text() + '|';
							// console.log('v----'+$(this).attr('myid'));
							jhd += $(this).attr('myid') + '-|';
						}
					});

					$('input[name="jhd"]').val(jhd);	

					
					//所选的子交货地
					var jhd2 = '|';
					$('#jhd_wraper a').each(function(k, v){
						if($(this).hasClass('xuan_cur')){
							// cd2 += $(this).text() + '|';
							// console.log('v----'+$(this).attr('myid'));
							jhd2 += $(this).attr('pid')+'-'+$(this).attr('myid') + '-|';
						}
						
					});

					$('input[name="jhd2"]').val(jhd2);
				}


				//提交表单
				$('#tijiaoClick').click(function(){
					setForm();
					//合约筛选置空
					$('input[name="heyue"]').val('');
						$('#url').val($('#fm1').serialize());
					$('#fm1').submit();
				});
			</script>
		</div>
</body>


</html>
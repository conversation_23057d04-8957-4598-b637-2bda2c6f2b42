<?xml version="1.0" encoding="UTF-8"?>
<phpunit
         verbose="false"
         stopOnError="false"
         stopOnFailure="false"
         stopOnIncomplete="false"
         stopOnSkipped="false"
         convertErrorsToExceptions="true"
         convertNoticesToExceptions="true"
         convertWarningsToExceptions="true"
         colors="false"
         forceCoversAnnotation="false"
         strict="false"
         processIsolation="false">
    <testsuites>
        <testsuite name="PHPMailer Tests">
            <directory>./test/</directory>
        </testsuite>
    </testsuites>
    <filter>
        <blacklist>
            <directory suffix=".php">./extras</directory>
        </blacklist>
    </filter>
    <groups>
        <exclude>
            <group>languages</group>
        </exclude>
    </groups>
    <logging>
        <log type="coverage-text" target="php://stdout" showUncoveredFiles="true"/>
        <log type="coverage-clover" target="build/logs/clover.xml"/>
        <log type="junit" target="build/logs/junit.xml" logIncompleteSkipped="false"/>
    </logging>
</phpunit>

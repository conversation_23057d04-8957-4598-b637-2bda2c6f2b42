-- 为超市表添加升贴水相关字段

-- 检查字段是否存在，如果不存在则添加
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'sd_chaoshi' 
     AND column_name = 'premium_discount' 
     AND table_schema = DATABASE()) > 0,
    'SELECT "premium_discount field already exists"',
    'ALTER TABLE `sd_chaoshi` ADD COLUMN `premium_discount` DECIMAL(10,2) DEFAULT 0 COMMENT "升贴水值"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'sd_chaoshi' 
     AND column_name = 'calculated_jicha' 
     AND table_schema = DATABASE()) > 0,
    'SELECT "calculated_jicha field already exists"',
    'ALTER TABLE `sd_chaoshi` ADD COLUMN `calculated_jicha` DECIMAL(10,2) DEFAULT 0 COMMENT "调整后基差"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加索引以提高查询性能
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_name = 'sd_chaoshi' 
     AND index_name = 'idx_premium_discount' 
     AND table_schema = DATABASE()) > 0,
    'SELECT "idx_premium_discount index already exists"',
    'ALTER TABLE `sd_chaoshi` ADD INDEX `idx_premium_discount` (`premium_discount`)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_name = 'sd_chaoshi' 
     AND index_name = 'idx_calculated_jicha' 
     AND table_schema = DATABASE()) > 0,
    'SELECT "idx_calculated_jicha index already exists"',
    'ALTER TABLE `sd_chaoshi` ADD INDEX `idx_calculated_jicha` (`calculated_jicha`)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 显示表结构确认
DESCRIBE `sd_chaoshi`;

-- 升贴水计算规则优化版数据库结构

-- 创建条件字段管理表
CREATE TABLE `sd_premium_discount_condition_field`
(
    `id`          INT          NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `field_code`  VARCHAR(50)  NOT NULL COMMENT '字段代码(如weight, region)',
    `field_name`  VARCHAR(100) NOT NULL COMMENT '字段名称(如重量, 地区)',
    `field_type`  ENUM('number', 'string', 'select') NOT NULL DEFAULT 'string' COMMENT '字段类型(number=数值型, string=字符串型, select=选择型)',
    `field_unit`  VARCHAR(20)  NULL COMMENT '字段单位(如kg, %, 等)',
    `select_options` TEXT NULL COMMENT '选择项配置(JSON格式，当field_type=select时使用)',
    `is_range`    TINYINT(1)   NOT NULL DEFAULT 0 COMMENT '是否支持范围(1=支持范围, 0=单值)',
    `is_enabled`  TINYINT(1)   NOT NULL DEFAULT 1 COMMENT '是否启用(1=启用, 0=禁用)',
    `sort_order`  INT          NOT NULL DEFAULT 100 COMMENT '排序权重',
    `created_at`  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_field_code` (`field_code`) COMMENT '字段代码唯一索引',
    KEY `idx_enabled_sort` (`is_enabled`, `sort_order`) COMMENT '启用状态和排序索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='升贴水条件字段管理表';

-- 创建升贴水计算规则表
CREATE TABLE `sd_premium_discount_rule`
(
    `id`         INT          NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `rule_name`  VARCHAR(255) NOT NULL COMMENT '规则名称',
    `is_enabled` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用(1=启用, 0=禁用)',
    `created_at` TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_rule_name` (`rule_name`) COMMENT '规则名称唯一索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='升贴水计算规则表';

-- 创建升贴水规则详情表（支持范围）
CREATE TABLE `sd_premium_discount_rule_detail`
(
    `id`              INT            NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `rule_id`         INT            NOT NULL COMMENT '关联规则ID',
    `field_id`        INT            NOT NULL COMMENT '关联条件字段ID',
    `condition_type`  ENUM('single', 'range') NOT NULL DEFAULT 'single' COMMENT '条件类型(single=单值, range=范围)',
    `operator`        ENUM('>', '<', '=', '>=', '<=', '!=', 'between', 'in') NOT NULL COMMENT '条件操作符',
    `condition_value` VARCHAR(255)   NULL COMMENT '条件值(单值时使用)',
    `min_value`       VARCHAR(100)   NULL COMMENT '最小值(范围时使用)',
    `max_value`       VARCHAR(100)   NULL COMMENT '最大值(范围时使用)',
    `discount_value`  DECIMAL(10, 2) NOT NULL COMMENT '升贴水值',
    `created_at`      TIMESTAMP      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`      TIMESTAMP      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    PRIMARY KEY (`id`),
    KEY               `idx_rule_id` (`rule_id`) COMMENT '规则ID索引',
    KEY               `idx_field_id` (`field_id`) COMMENT '字段ID索引',
    FOREIGN KEY (`rule_id`) REFERENCES `sd_premium_discount_rule`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`field_id`) REFERENCES `sd_premium_discount_condition_field`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='升贴水规则详情表';


INSERT INTO `sql_mianzhida`.`sd_premium_discount_condition_field` (`id`, `field_code`, `field_name`, `field_type`, `field_unit`, `select_options`, `is_range`, `is_enabled`, `sort_order`, `created_at`, `updated_at`) VALUES (1, 'weight', '重量', 'number', 'kg', NULL, 1, 0, 10, '2025-08-04 05:43:10', '2025-08-04 14:19:06');
INSERT INTO `sql_mianzhida`.`sd_premium_discount_condition_field` (`id`, `field_code`, `field_name`, `field_type`, `field_unit`, `select_options`, `is_range`, `is_enabled`, `sort_order`, `created_at`, `updated_at`) VALUES (2, 'moisture', '水分', 'number', '%', NULL, 1, 0, 20, '2025-08-04 05:43:10', '2025-08-04 14:19:11');
INSERT INTO `sql_mianzhida`.`sd_premium_discount_condition_field` (`id`, `field_code`, `field_name`, `field_type`, `field_unit`, `select_options`, `is_range`, `is_enabled`, `sort_order`, `created_at`, `updated_at`) VALUES (3, 'impurity', '含杂', 'number', '%', NULL, 1, 1, 30, '2025-08-04 05:43:10', '2025-08-04 14:18:01');
INSERT INTO `sql_mianzhida`.`sd_premium_discount_condition_field` (`id`, `field_code`, `field_name`, `field_type`, `field_unit`, `select_options`, `is_range`, `is_enabled`, `sort_order`, `created_at`, `updated_at`) VALUES (4, 'grade', '等级', 'select', NULL, '[\"A级\",\"B级\",\"C级\",\"D级\"]', 0, 0, 40, '2025-08-04 05:43:10', '2025-08-04 14:19:16');
INSERT INTO `sql_mianzhida`.`sd_premium_discount_condition_field` (`id`, `field_code`, `field_name`, `field_type`, `field_unit`, `select_options`, `is_range`, `is_enabled`, `sort_order`, `created_at`, `updated_at`) VALUES (5, 'region', '地区', 'select', NULL, '[\"新疆\",\"内地\",\"进口\"]', 0, 1, 50, '2025-08-04 05:43:10', '2025-08-04 05:43:10');
INSERT INTO `sql_mianzhida`.`sd_premium_discount_condition_field` (`id`, `field_code`, `field_name`, `field_type`, `field_unit`, `select_options`, `is_range`, `is_enabled`, `sort_order`, `created_at`, `updated_at`) VALUES (6, 'color', '颜色级', 'select', NULL, '[\"白棉1级\",\"白棉2级\",\"白棉3级\",\"淡点污棉1级\",\"淡点污棉2级\",\"淡点污棉3级\"]', 0, 1, 60, '2025-08-04 05:43:10', '2025-08-04 05:43:10');
INSERT INTO `sql_mianzhida`.`sd_premium_discount_condition_field` (`id`, `field_code`, `field_name`, `field_type`, `field_unit`, `select_options`, `is_range`, `is_enabled`, `sort_order`, `created_at`, `updated_at`) VALUES (7, 'length', '长度', 'number', 'mm', NULL, 1, 1, 70, '2025-08-04 05:43:10', '2025-08-04 05:43:10');
INSERT INTO `sql_mianzhida`.`sd_premium_discount_condition_field` (`id`, `field_code`, `field_name`, `field_type`, `field_unit`, `select_options`, `is_range`, `is_enabled`, `sort_order`, `created_at`, `updated_at`) VALUES (8, 'strength', '强力', 'number', 'cN/tex', NULL, 1, 1, 80, '2025-08-04 05:43:10', '2025-08-04 05:43:10');

INSERT INTO `sql_mianzhida`.`sd_premium_discount_rule` (`id`, `rule_name`, `is_enabled`, `created_at`, `updated_at`) VALUES (1, '测试', 1, '2025-08-04 13:52:46', '2025-08-04 14:11:39');

INSERT INTO `sql_mianzhida`.`sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (1, 1, 6, 'single', '=', '白棉1级', NULL, NULL, 200.00, '2025-08-04 15:13:03', '2025-08-04 15:13:03');
INSERT INTO `sql_mianzhida`.`sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (2, 1, 6, 'single', '=', '白棉2级', NULL, NULL, 100.00, '2025-08-04 15:13:18', '2025-08-04 15:13:18');
INSERT INTO `sql_mianzhida`.`sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (3, 1, 6, 'single', '=', '白棉3级', NULL, NULL, 50.00, '2025-08-04 15:13:36', '2025-08-04 15:13:36');
INSERT INTO `sql_mianzhida`.`sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (4, 1, 6, 'single', '=', '白棉5级', NULL, NULL, -500.00, '2025-08-04 15:15:44', '2025-08-04 15:15:44');
INSERT INTO `sql_mianzhida`.`sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (5, 1, 6, 'single', '=', '淡点污棉1级', NULL, NULL, -150.00, '2025-08-04 15:16:10', '2025-08-04 15:16:10');
INSERT INTO `sql_mianzhida`.`sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (6, 1, 6, 'single', '=', '淡点污棉2级', NULL, NULL, -400.00, '2025-08-04 15:16:25', '2025-08-04 15:16:25');
INSERT INTO `sql_mianzhida`.`sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (7, 1, 6, 'single', '=', '淡点污棉3级', NULL, NULL, -800.00, '2025-08-04 15:16:43', '2025-08-04 15:16:43');
INSERT INTO `sql_mianzhida`.`sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (8, 1, 6, 'single', '=', '淡黄染棉1级', NULL, NULL, -700.00, '2025-08-04 15:17:00', '2025-08-04 15:17:00');
INSERT INTO `sql_mianzhida`.`sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (9, 1, 6, 'single', '=', '淡黄染棉2级', NULL, NULL, -1100.00, '2025-08-04 15:17:37', '2025-08-04 15:17:37');
INSERT INTO `sql_mianzhida`.`sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (10, 1, 6, 'single', '=', '淡黄染棉3级', NULL, NULL, -1600.00, '2025-08-04 15:17:52', '2025-08-04 15:17:52');
INSERT INTO `sql_mianzhida`.`sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (12, 1, 7, 'range', 'between', NULL, '26', '26.9', -850.00, '2025-08-04 15:48:53', '2025-08-04 15:48:53');
INSERT INTO `sql_mianzhida`.`sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (13, 1, 7, 'range', 'between', NULL, '27', '27.9', -250.00, '2025-08-04 15:49:24', '2025-08-04 15:49:24');
INSERT INTO `sql_mianzhida`.`sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (14, 1, 7, 'range', 'between', NULL, '29', '29.9', 50.00, '2025-08-04 15:49:52', '2025-08-04 15:49:52');
INSERT INTO `sql_mianzhida`.`sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (15, 1, 7, 'range', 'between', NULL, '30', '30.9', 100.00, '2025-08-04 15:50:14', '2025-08-04 15:50:14');
INSERT INTO `sql_mianzhida`.`sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (16, 1, 7, 'range', 'between', NULL, '31', '31.9', 150.00, '2025-08-04 15:50:57', '2025-08-04 15:50:57');
INSERT INTO `sql_mianzhida`.`sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (18, 1, 7, 'single', '', '32', NULL, NULL, 200.00, '2025-08-04 15:57:54', '2025-08-04 15:57:54');

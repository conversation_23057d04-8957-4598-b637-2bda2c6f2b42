<?php
// router.php - 模拟 Nginx 重写规则
$documentRoot = __DIR__;
$requestUri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

// 1. 检查请求的文件/目录是否存在
$filePath = $documentRoot . $requestUri;

// 2. 如果文件存在（包括目录），让 PHP 服务器直接处理
if (file_exists($filePath)) {
    return false; // 关键！让内置服务器处理静态文件
}

// 3. 如果文件不存在，重写到 index.php
$_GET['s'] = ltrim($requestUri, '/'); // 传递原始路径（去掉开头的 /）
$_SERVER['SCRIPT_NAME'] = '/index.php';
$_SERVER['SCRIPT_FILENAME'] = $documentRoot . '/index.php';

// 4. 保留原始查询参数（如果存在）
if (!empty($_SERVER['QUERY_STRING'])) {
    parse_str($_SERVER['QUERY_STRING'], $queryParams);
    $_GET = array_merge($_GET, $queryParams);
}

// 5. 包含 index.php 并终止脚本
require $documentRoot . '/index.php';
exit;
